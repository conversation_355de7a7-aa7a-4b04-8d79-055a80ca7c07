package org.icbs

import grails.testing.gorm.DomainUnitTest
import grails.testing.services.ServiceUnitTest
import spock.lang.Specification
import spock.lang.Unroll
import org.icbs.common.CommonUtilityService
import org.icbs.search.UnifiedSearchService
import org.icbs.validation.UnifiedValidationService
import org.icbs.security.SecureUser
import org.icbs.security.JwtTokenService
import org.icbs.security.EncryptionService

/**
 * Consolidated Test Suite
 * Tests all consolidated services and eliminates duplicate test code
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 6.2.3
 */
class ConsolidatedTestSuite extends Specification 
    implements ServiceUnitTest<CommonUtilityService> {
    
    UnifiedSearchService unifiedSearchService
    UnifiedValidationService unifiedValidationService
    JwtTokenService jwtTokenService
    EncryptionService encryptionService
    
    def setup() {
        // Mock services
        unifiedSearchService = Mock(UnifiedSearchService)
        unifiedValidationService = Mock(UnifiedValidationService)
        jwtTokenService = Mock(JwtTokenService)
        encryptionService = Mock(EncryptionService)
        
        // Setup test data
        mockDomains(SecureUser)
    }
    
    // =====================================================
    // COMMON UTILITY SERVICE TESTS
    // =====================================================
    
    def "test CommonUtilityService duplicate detection"() {
        given: "test data"
        def testClass = SecureUser
        def criteria = [username: 'testuser', email: '<EMAIL>']
        
        when: "checking for duplicates"
        boolean hasDuplicates = service.checkForDuplicates(testClass, criteria)
        
        then: "should return false for no duplicates"
        !hasDuplicates
    }
    
    def "test CommonUtilityService safe string comparison"() {
        expect: "safe string comparison works correctly"
        service.safeStringEquals(str1, str2) == expected
        
        where:
        str1      | str2      | expected
        "test"    | "test"    | true
        "Test"    | "test"    | true
        " test "  | "test"    | true
        null      | null      | true
        null      | "test"    | false
        "test"    | null      | false
        ""        | ""        | true
    }
    
    def "test CommonUtilityService unique code generation"() {
        when: "generating unique codes"
        String code1 = service.generateUniqueCode("TEST", 6)
        String code2 = service.generateUniqueCode("TEST", 6)
        
        then: "codes should be unique and have correct format"
        code1 != code2
        code1.startsWith("TEST")
        code1.length() == 10 // TEST + 6 digits
        code2.startsWith("TEST")
        code2.length() == 10
    }
    
    def "test CommonUtilityService currency formatting"() {
        expect: "currency formatting works correctly"
        service.formatCurrency(amount, currency) == expected
        
        where:
        amount          | currency | expected
        new BigDecimal("100.50") | "USD"    | "100.50 USD"
        new BigDecimal("0")      | "EUR"    | "0.00 EUR"
        null            | "USD"    | "0.00"
    }
    
    def "test CommonUtilityService input cleaning"() {
        expect: "input cleaning works correctly"
        service.cleanInput(input) == expected
        
        where:
        input           | expected
        "normal text"   | "normal text"
        " spaced text " | "spaced text"
        "<script>alert('xss')</script>" | "scriptalert('xss')/script"
        null            | null
        ""              | null
    }
    
    // =====================================================
    // VALIDATION SERVICE TESTS
    // =====================================================
    
    def "test email validation"() {
        given: "validation service"
        def validationService = new UnifiedValidationService()
        
        expect: "email validation works correctly"
        validationService.isValidEmail(email) == expected
        
        where:
        email                    | expected
        "<EMAIL>"       | true
        "<EMAIL>" | true
        "invalid.email"          | false
        "test@"                  | false
        "@domain.com"            | false
        null                     | false
        ""                       | false
    }
    
    def "test phone validation"() {
        given: "validation service"
        def validationService = new UnifiedValidationService()
        
        expect: "phone validation works correctly"
        validationService.isValidPhone(phone) == expected
        
        where:
        phone              | expected
        "**********"       | true
        "(*************"   | true
        "******-456-7890"  | true
        "123"              | false
        "*****************" | false
        null               | false
        ""                 | false
    }
    
    def "test account number validation"() {
        given: "validation service"
        def validationService = new UnifiedValidationService()
        
        expect: "account number validation works correctly"
        validationService.validateAccountNumber(accountNumber, accountType) == expected
        
        where:
        accountNumber    | accountType | expected
        "**********"     | null        | true
        "**********1234" | null        | true
        "**********"     | "SAVINGS"   | true
        "**********"     | "CHECKING"  | true
        "**********"     | "LOAN"      | true
        "*********"      | null        | false  // Too short
        "*****************" | null     | false  // Too long
        "abcd567890"     | null        | false  // Non-numeric
        null             | null        | false
    }
    
    def "test field validation with constraints"() {
        given: "validation service"
        def validationService = new UnifiedValidationService()
        
        when: "validating field with constraints"
        def result = validationService.validateField("testField", value, constraints)
        
        then: "validation result should match expected"
        result.isValid == expectedValid
        
        where:
        value    | constraints                              | expectedValid
        "test"   | [required: true, minLength: 3]         | true
        "te"     | [required: true, minLength: 3]         | false
        ""       | [required: true]                        | false
        "test"   | [required: false]                       | true
        "123"    | [numeric: true, min: 100, max: 200]    | false
        "150"    | [numeric: true, min: 100, max: 200]    | true
    }
    
    // =====================================================
    // SECURITY TESTS
    // =====================================================
    
    def "test SecureUser creation"() {
        when: "creating a secure user"
        def user = new SecureUser(
            username: 'testuser',
            password: 'TestPassword123!',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            employeeId: 'EMP001'
        )
        
        then: "user should be valid"
        user.validate()
        user.username == 'testuser'
        user.fullName == 'Test User'
    }
    
    @Unroll
    def "test SecureUser validation for invalid #field"() {
        given: "a user with invalid data"
        def user = new SecureUser(
            username: username,
            password: password,
            email: email,
            firstName: firstName,
            lastName: lastName
        )
        
        when: "validating the user"
        def isValid = user.validate()
        
        then: "validation should fail"
        !isValid
        user.errors.hasFieldErrors(field)
        
        where:
        field        | username    | password        | email              | firstName | lastName
        'username'   | ''          | 'ValidPass123!' | '<EMAIL>' | 'Test'    | 'User'
        'username'   | 'ab'        | 'ValidPass123!' | '<EMAIL>' | 'Test'    | 'User'
        'password'   | 'testuser'  | ''              | '<EMAIL>' | 'Test'    | 'User'
        'email'      | 'testuser'  | 'ValidPass123!' | 'invalid-email'    | 'Test'    | 'User'
        'firstName'  | 'testuser'  | 'ValidPass123!' | '<EMAIL>' | ''        | 'User'
        'lastName'   | 'testuser'  | 'ValidPass123!' | '<EMAIL>' | 'Test'    | ''
    }
    
    def "test password encryption and verification"() {
        given: "encryption service"
        def realEncryptionService = new EncryptionService()
        
        when: "hashing password"
        String plainPassword = "TestPassword123!"
        String hashedPassword = realEncryptionService.hashPassword(plainPassword)
        
        then: "password should be hashed correctly"
        hashedPassword != plainPassword
        hashedPassword.startsWith('$2a$') || hashedPassword.startsWith('$2b$')
        realEncryptionService.verifyPassword(plainPassword, hashedPassword)
        !realEncryptionService.verifyPassword('wrongpassword', hashedPassword)
    }
    
    def "test data masking"() {
        given: "encryption service"
        def realEncryptionService = new EncryptionService()
        
        expect: "data should be masked correctly"
        realEncryptionService.maskSensitiveData(input, type) == expected
        
        where:
        input              | type            | expected
        "**********"       | "ACCOUNT_NUMBER"| "******7890"
        "***********"      | "SSN"           | "***-**-6789"
        "<EMAIL>" | "EMAIL"         | "t***@example.com"
        "************"     | "PHONE"         | "*******4567"
    }
    
    def "test password strength validation"() {
        given: "encryption service"
        def realEncryptionService = new EncryptionService()
        
        expect: "password strength should be validated correctly"
        def result = realEncryptionService.validatePasswordStrength(password)
        result.isValid == expectedValid
        result.strength == expectedStrength
        
        where:
        password           | expectedValid | expectedStrength
        "TestPass123!"     | true          | "STRONG"
        "weakpass"         | false         | "VERY_WEAK"
        "TestPassword"     | false         | "WEAK"
        "TestPass123"      | false         | "MEDIUM"
        "VeryStrongPass1!" | true          | "VERY_STRONG"
    }
    
    // =====================================================
    // INTEGRATION TESTS
    // =====================================================
    
    def "test service integration"() {
        given: "multiple services working together"
        def commonService = new CommonUtilityService()
        def validationService = new UnifiedValidationService()
        
        when: "performing integrated operations"
        String cleanedInput = commonService.cleanInput(" <EMAIL> ")
        boolean isValidEmail = validationService.isValidEmail(cleanedInput)
        String uniqueCode = commonService.generateUniqueCode("USR", 4)
        
        then: "all operations should work correctly"
        cleanedInput == "<EMAIL>"
        isValidEmail
        uniqueCode.startsWith("USR")
        uniqueCode.length() == 7 // USR + 4 digits
    }
    
    def "test error handling in services"() {
        given: "services with potential error conditions"
        def commonService = new CommonUtilityService()
        
        when: "calling methods with invalid parameters"
        boolean result1 = commonService.checkForDuplicates(null, [:])
        String result2 = commonService.cleanInput(null)
        String result3 = commonService.formatCurrency(null)
        
        then: "services should handle errors gracefully"
        !result1  // Should return false for null class
        result2 == null  // Should return null for null input
        result3 == "0.00"  // Should return default for null amount
    }
    
    // =====================================================
    // PERFORMANCE TESTS
    // =====================================================
    
    def "test service performance"() {
        given: "services for performance testing"
        def commonService = new CommonUtilityService()
        
        when: "performing multiple operations"
        long startTime = System.currentTimeMillis()
        
        100.times {
            commonService.generateUniqueCode("TEST", 6)
            commonService.safeStringEquals("test", "TEST")
            commonService.cleanInput("<script>test</script>")
        }
        
        long endTime = System.currentTimeMillis()
        long duration = endTime - startTime
        
        then: "operations should complete within reasonable time"
        duration < 1000 // Should complete within 1 second
    }
}
