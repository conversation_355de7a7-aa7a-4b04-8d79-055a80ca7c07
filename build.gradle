plugins {
    id "groovy"
    id "com.github.erdi.webdriver-binaries" version "3.2"
    id "war"
    id "idea"
    id "com.bertramlabs.asset-pipeline" version "4.5.1"
    id "application"
    id "eclipse"
}

// Not Published to Gradle Plugin Portal
apply plugin: "org.grails.grails-web"
apply plugin: "org.grails.grails-gsp"

group = "org.icbs"

repositories {
    mavenCentral()
    maven { url "https://repo.grails.org/grails/core/" }
}

configurations {
    all {
        resolutionStrategy.eachDependency { DependencyResolveDetails details->
            if (details.requested.group == 'org.seleniumhq.selenium') {
                details.useVersion('4.19.1')
            }
        }
    }
}

dependencies {
    profile "org.grails.profiles:web"
    developmentOnly "org.springframework.boot:spring-boot-devtools"
    implementation "io.github.virtualdogbert:logback-groovy-config:1.12.4"
    implementation "org.grails:grails-core"
    implementation "org.grails:grails-logging"
    implementation "org.grails:grails-plugin-databinding"
    implementation "org.grails:grails-plugin-i18n"
    implementation "org.grails:grails-plugin-interceptors"
    implementation "org.grails:grails-plugin-rest"
    implementation "org.grails:grails-plugin-services"
    implementation "org.grails:grails-plugin-url-mappings"
    implementation "org.grails:grails-web-boot"
    implementation "org.grails.plugins:cache:7.0.0"
    implementation "org.grails.plugins:database-migration:4.2.1"
    implementation "org.grails.plugins:gsp"
    implementation "org.grails.plugins:hibernate5:8.0.0"
    implementation "org.grails.plugins:quartz:3.0.0"
    implementation "org.grails.plugins:scaffolding"

    // GRAILS 7.x COMPATIBILITY UPDATES
    implementation "org.grails:grails-plugin-validation"
    implementation "org.grails:grails-plugin-codecs"
    implementation "org.grails:grails-plugin-filters"
    implementation "org.grails:grails-plugin-gsp"
    
    // Apache POI for Excel operations
    implementation 'org.apache.poi:poi:5.2.3'
    implementation 'org.apache.poi:poi-ooxml:5.2.3'
    
    // Apache Commons Codec
    implementation 'commons-codec:commons-codec:1.15'
    
    // JSON library for JSONObject
    implementation 'org.json:json:20231013'

    // SECURITY DEPENDENCIES - CRITICAL SECURITY FIXES
    implementation 'org.springframework.security:spring-security-crypto:6.2.1'
    implementation 'org.springframework.security:spring-security-web:6.2.1'
    implementation 'org.springframework.security:spring-security-config:6.2.1'
    implementation 'org.springframework.boot:spring-boot-starter-security:3.2.1'
    implementation 'com.zaxxer:HikariCP:5.1.0'
    implementation 'org.springframework.boot:spring-boot-starter-cache:3.2.1'
    implementation 'com.github.ben-manes.caffeine:caffeine:3.1.8'

    // JWT AUTHENTICATION DEPENDENCIES
    implementation 'io.jsonwebtoken:jjwt-api:0.12.3'
    implementation 'io.jsonwebtoken:jjwt-impl:0.12.3'
    implementation 'io.jsonwebtoken:jjwt-jackson:0.12.3'

    // SPRING SECURITY GRAILS INTEGRATION
    implementation 'org.grails.plugins:spring-security-core:6.0.0'
    implementation 'org.grails.plugins:spring-security-rest:4.0.0'

    // ENCRYPTION AND SECURITY UTILITIES
    implementation 'org.bouncycastle:bcprov-jdk18on:1.77'
    implementation 'org.bouncycastle:bcpkix-jdk18on:1.77'

    // Grails compatibility dependencies
    implementation "org.grails:grails-web-common"
    implementation "org.grails:grails-web-url-mappings"
    implementation "org.grails:grails-web-mvc"
    implementation "org.grails:grails-web-gsp"
    implementation "org.grails:grails-web-databinding"
    implementation "org.grails:grails-web-jsp"

    implementation "org.hibernate:hibernate-validator:6.2.5.Final"
    implementation "org.springframework.boot:spring-boot-autoconfigure"
    implementation "org.springframework.boot:spring-boot-starter"
    implementation "org.springframework.boot:spring-boot-starter-actuator"
    implementation "org.springframework.boot:spring-boot-starter-logging"
    implementation "org.springframework.boot:spring-boot-starter-undertow"
    implementation "org.springframework.boot:spring-boot-starter-validation"
    compileOnly "io.micronaut:micronaut-inject-groovy"
    console "org.grails:grails-console"
    runtimeOnly "com.bertramlabs.plugins:asset-pipeline-grails:4.5.1"
    runtimeOnly "org.apache.tomcat:tomcat-jdbc"
    runtimeOnly "org.fusesource.jansi:jansi:1.18"
    runtimeOnly "org.grails.plugins:grails-console:2.1.1"
    runtimeOnly "org.postgresql:postgresql"
    testImplementation "io.micronaut:micronaut-inject-groovy"
    testImplementation "org.grails:grails-gorm-testing-support"
    testImplementation "org.grails:grails-web-testing-support"
    testImplementation "org.grails.plugins:geb"
    testImplementation "org.seleniumhq.selenium:selenium-api:4.19.1"
    testImplementation "org.seleniumhq.selenium:selenium-remote-driver:4.19.1"
    testImplementation "org.seleniumhq.selenium:selenium-support:4.19.1"
    testImplementation "org.spockframework:spock-core"
    testImplementation "org.testcontainers:postgresql"
    testImplementation "org.testcontainers:spock"
    testImplementation "org.testcontainers:testcontainers"
    testRuntimeOnly "org.seleniumhq.selenium:selenium-chrome-driver:4.19.1"
    testRuntimeOnly "org.seleniumhq.selenium:selenium-firefox-driver:4.19.1"
    testRuntimeOnly "org.seleniumhq.selenium:selenium-safari-driver:4.19.1"
}

application {
    mainClass = "org.icbs.Application"
}

java {
    sourceCompatibility = JavaVersion.toVersion("17")
}

tasks.withType(Test) {
    useJUnitPlatform()
    systemProperty "geb.env", System.getProperty('geb.env')
    systemProperty "geb.build.reportsDir", reporting.file("geb/integrationTest")
    systemProperty 'webdriver.chrome.driver', "${System.getenv('CHROMEWEBDRIVER')}/chromedriver"
    systemProperty 'webdriver.gecko.driver', "${System.getenv('GECKOWEBDRIVER')}/geckodriver"
}
webdriverBinaries {
    chromedriver '122.0.6260.0'
    geckodriver '0.33.0'
    edgedriver '110.0.1587.57'
}
assets {
    minifyJs = true
    minifyCss = true
    skipNonDigests = true
}
