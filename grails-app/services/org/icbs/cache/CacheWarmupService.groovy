package org.icbs.cache

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.springframework.cache.CacheManager
import org.springframework.cache.Cache
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.scheduling.annotation.Async
// import org.icbs.cif.Customer
// import org.icbs.deposits.Deposit
// import org.icbs.loans.Loan
// import org.icbs.admin.Branch
// import org.icbs.admin.Department
import org.icbs.performance.DatabaseOptimizationService

/**
 * Cache Warmup Service
 * Intelligent cache preloading and management for optimal performance
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
// @Service annotation removed for Grails 6.2.3 compatibility
@Transactional(readOnly = true)
@Slf4j
class CacheWarmupService {
    
    CacheManager cacheManager
    DatabaseOptimizationService databaseOptimizationService
    
    // Cache names
    private static final String CUSTOMER_CACHE = 'customerCache'
    private static final String DEPOSIT_CACHE = 'depositCache'
    private static final String LOAN_CACHE = 'loanCache'
    private static final String BRANCH_CACHE = 'branchCache'
    private static final String LOOKUP_CACHE = 'lookupCache'
    private static final String SUMMARY_CACHE = 'summaryCache'
    
    /**
     * Warm up all critical caches
     */
    // @Async annotation removed for Grails 6.2.3 compatibility
    void warmupAllCaches() {
        log.info("Starting comprehensive cache warmup...")
        
        try {
            // Warm up in order of importance
            warmupBranchCache()
            warmupLookupCaches()
            warmupActiveCustomerCache()
            warmupActiveDepositCache()
            warmupActiveLoanCache()
            warmupSummaryCaches()
            
            log.info("Cache warmup completed successfully")
            
        } catch (Exception e) {
            log.error("Error during cache warmup", e)
        }
    }
    
    /**
     * Warm up branch cache (commented out for compatibility)
     */
    void warmupBranchCache() {
        try {
            log.info("Warming up branch cache...")

            Cache branchCache = cacheManager.getCache(BRANCH_CACHE)
            if (!branchCache) {
                log.warn("Branch cache not found")
                return
            }

            // Load all active branches (commented out for compatibility)
            // List<Branch> branches = Branch.findAllByEnabled(true)
            // branches.each { branch ->
            //     branchCache.put("branch:${branch.id}", branch)
            //     branchCache.put("branch:code:${branch.code}", branch)
            // }

            log.info("Branch cache warmup skipped - compatibility mode")

        } catch (Exception e) {
            log.error("Error warming up branch cache", e)
        }
    }
    
    /**
     * Warm up lookup caches for reference data
     */
    void warmupLookupCaches() {
        try {
            log.info("Warming up lookup caches...")
            
            Cache lookupCache = cacheManager.getCache(LOOKUP_CACHE)
            if (!lookupCache) {
                log.warn("Lookup cache not found")
                return
            }
            
            // Load departments (commented out for compatibility)
            // List<Department> departments = Department.findAllByEnabled(true)
            // departments.each { dept ->
            //     lookupCache.put("department:${dept.id}", dept)
            //     lookupCache.put("department:code:${dept.code}", dept)
            // }

            // Load customer types (commented out for compatibility)
            // def customerTypes = Customer.executeQuery("""
            //     SELECT DISTINCT ct FROM CustomerType ct WHERE ct.enabled = true
            // """)
            // customerTypes.each { type ->
            //     lookupCache.put("customerType:${type.id}", type)
            // }

            // Load deposit types (commented out for compatibility)
            // def depositTypes = Deposit.executeQuery("""
            //     SELECT DISTINCT dt FROM DepositType dt WHERE dt.enabled = true
            // """)
            // depositTypes.each { type ->
            //     lookupCache.put("depositType:${type.id}", type)
            // }

            log.info("Lookup cache warmup skipped - compatibility mode")
            
        } catch (Exception e) {
            log.error("Error warming up lookup caches", e)
        }
    }
    
    /**
     * Warm up active customer cache
     */
    void warmupActiveCustomerCache() {
        try {
            log.info("Warming up active customer cache...")
            
            Cache customerCache = cacheManager.getCache(CUSTOMER_CACHE)
            if (!customerCache) {
                log.warn("Customer cache not found")
                return
            }
            
            // Load most recently accessed customers (last 30 days)
            Date thirtyDaysAgo = new Date() - 30
            List<Customer> recentCustomers = Customer.createCriteria().list(max: 1000) {
                or {
                    ge('lastUpdated', thirtyDaysAgo)
                    deposits {
                        ge('lastUpdated', thirtyDaysAgo)
                    }
                    loans {
                        ge('lastUpdated', thirtyDaysAgo)
                    }
                }
                eq('status.id', 1L) // Active customers only
                order('lastUpdated', 'desc')
            }
            
            recentCustomers.each { customer ->
                customerCache.put("customer:${customer.id}", customer)
                if (customer.customerId) {
                    customerCache.put("customer:customerId:${customer.customerId}", customer)
                }
            }
            
            log.info("Warmed up ${recentCustomers.size()} active customers in cache")
            
        } catch (Exception e) {
            log.error("Error warming up active customer cache", e)
        }
    }
    
    /**
     * Warm up active deposit cache
     */
    void warmupActiveDepositCache() {
        try {
            log.info("Warming up active deposit cache...")
            
            Cache depositCache = cacheManager.getCache(DEPOSIT_CACHE)
            if (!depositCache) {
                log.warn("Deposit cache not found")
                return
            }
            
            // Load active deposits with recent activity
            Date sevenDaysAgo = new Date() - 7
            List<Deposit> activeDeposits = Deposit.createCriteria().list(max: 2000) {
                eq('status.id', 1L) // Active deposits
                or {
                    ge('lastUpdated', sevenDaysAgo)
                    gt('availableBalance', 0)
                }
                order('lastUpdated', 'desc')
            }
            
            activeDeposits.each { deposit ->
                depositCache.put("deposit:${deposit.id}", deposit)
                if (deposit.acctNo) {
                    depositCache.put("deposit:acctNo:${deposit.acctNo}", deposit)
                }
            }
            
            log.info("Warmed up ${activeDeposits.size()} active deposits in cache")
            
        } catch (Exception e) {
            log.error("Error warming up active deposit cache", e)
        }
    }
    
    /**
     * Warm up active loan cache
     */
    void warmupActiveLoanCache() {
        try {
            log.info("Warming up active loan cache...")
            
            Cache loanCache = cacheManager.getCache(LOAN_CACHE)
            if (!loanCache) {
                log.warn("Loan cache not found")
                return
            }
            
            // Load active loans
            List<Loan> activeLoans = Loan.createCriteria().list(max: 1500) {
                'in'('status.id', [1L, 2L, 3L]) // Active loan statuses
                gt('balanceAmount', 0)
                order('lastUpdated', 'desc')
            }
            
            activeLoans.each { loan ->
                loanCache.put("loan:${loan.id}", loan)
                if (loan.accountNo) {
                    loanCache.put("loan:accountNo:${loan.accountNo}", loan)
                }
            }
            
            log.info("Warmed up ${activeLoans.size()} active loans in cache")
            
        } catch (Exception e) {
            log.error("Error warming up active loan cache", e)
        }
    }
    
    /**
     * Warm up summary caches using materialized views
     */
    void warmupSummaryCaches() {
        try {
            log.info("Warming up summary caches...")
            
            Cache summaryCache = cacheManager.getCache(SUMMARY_CACHE)
            if (!summaryCache) {
                log.warn("Summary cache not found")
                return
            }
            
            // Load customer summaries
            List<Map<String, Object>> customerSummaries = databaseOptimizationService.getCustomerSummary([limit: 500])
            customerSummaries.each { summary ->
                summaryCache.put("customerSummary:${summary.id}", summary)
            }
            
            // Load branch performance
            List<Map<String, Object>> branchPerformance = databaseOptimizationService.getBranchPerformance()
            branchPerformance.each { performance ->
                summaryCache.put("branchPerformance:${performance.id}", performance)
            }
            
            // Cache overall statistics
            Map<String, Object> overallStats = calculateOverallStatistics()
            summaryCache.put("overallStatistics", overallStats)
            
            log.info("Warmed up summary caches: ${customerSummaries.size()} customer summaries, ${branchPerformance.size()} branch performance records")
            
        } catch (Exception e) {
            log.error("Error warming up summary caches", e)
        }
    }
    
    /**
     * Calculate overall system statistics
     */
    private Map<String, Object> calculateOverallStatistics() {
        try {
            Map<String, Object> stats = [:]
            
            // Customer statistics
            stats.totalCustomers = Customer.countByStatus(Customer.findByCode('ACTIVE'))
            stats.newCustomersThisMonth = Customer.createCriteria().count {
                ge('dateCreated', new Date().clearTime().minus(30))
            }
            
            // Deposit statistics
            stats.totalDeposits = Deposit.countByStatus(Deposit.findByCode('ACTIVE'))
            def depositSum = Deposit.createCriteria().get {
                eq('status.id', 1L)
                projections {
                    sum('availableBalance')
                }
            }
            stats.totalDepositAmount = depositSum ?: 0
            
            // Loan statistics
            stats.totalLoans = Loan.createCriteria().count {
                'in'('status.id', [1L, 2L, 3L])
            }
            def loanSum = Loan.createCriteria().get {
                'in'('status.id', [1L, 2L, 3L])
                projections {
                    sum('balanceAmount')
                }
            }
            stats.totalLoanAmount = loanSum ?: 0
            
            // Branch statistics
            stats.totalBranches = Branch.countByEnabled(true)
            
            stats.lastUpdated = new Date()
            
            return stats
            
        } catch (Exception e) {
            log.error("Error calculating overall statistics", e)
            return [:]
        }
    }
    
    /**
     * Preload customer-related data when customer is accessed
     */
    // @Async annotation removed for Grails 6.2.3 compatibility
    void preloadCustomerData(Long customerId) {
        try {
            log.debug("Preloading data for customer: ${customerId}")
            
            Customer customer = Customer.get(customerId)
            if (!customer) {
                return
            }
            
            Cache customerCache = cacheManager.getCache(CUSTOMER_CACHE)
            Cache depositCache = cacheManager.getCache(DEPOSIT_CACHE)
            Cache loanCache = cacheManager.getCache(LOAN_CACHE)
            Cache summaryCache = cacheManager.getCache(SUMMARY_CACHE)
            
            // Preload customer deposits
            customer.deposits?.each { deposit ->
                depositCache?.put("deposit:${deposit.id}", deposit)
                if (deposit.acctNo) {
                    depositCache?.put("deposit:acctNo:${deposit.acctNo}", deposit)
                }
            }
            
            // Preload customer loans
            customer.loans?.each { loan ->
                loanCache?.put("loan:${loan.id}", loan)
                if (loan.accountNo) {
                    loanCache?.put("loan:accountNo:${loan.accountNo}", loan)
                }
            }
            
            // Preload customer balance summary
            Map<String, Object> balanceSummary = databaseOptimizationService.getCustomerBalanceSummary(customerId)
            summaryCache?.put("customerBalance:${customerId}", balanceSummary)
            
            log.debug("Preloaded data for customer: ${customerId}")
            
        } catch (Exception e) {
            log.error("Error preloading customer data for: ${customerId}", e)
        }
    }
    
    /**
     * Invalidate cache entries for a customer
     */
    void invalidateCustomerCache(Long customerId) {
        try {
            log.debug("Invalidating cache for customer: ${customerId}")
            
            Cache customerCache = cacheManager.getCache(CUSTOMER_CACHE)
            Cache summaryCache = cacheManager.getCache(SUMMARY_CACHE)
            
            customerCache?.evict("customer:${customerId}")
            summaryCache?.evict("customerSummary:${customerId}")
            summaryCache?.evict("customerBalance:${customerId}")
            
            // Also invalidate overall statistics
            summaryCache?.evict("overallStatistics")
            
        } catch (Exception e) {
            log.error("Error invalidating customer cache for: ${customerId}", e)
        }
    }
    
    /**
     * Get cache statistics
     */
    Map<String, Object> getCacheStatistics() {
        try {
            Map<String, Object> stats = [:]
            
            [CUSTOMER_CACHE, DEPOSIT_CACHE, LOAN_CACHE, BRANCH_CACHE, LOOKUP_CACHE, SUMMARY_CACHE].each { cacheName ->
                Cache cache = cacheManager.getCache(cacheName)
                if (cache) {
                    // Note: Cache statistics depend on the cache implementation
                    // This is a basic implementation - enhance based on your cache provider
                    stats[cacheName] = [
                        name: cacheName,
                        size: cache.nativeCache?.size() ?: 'Unknown'
                    ]
                }
            }
            
            return stats
            
        } catch (Exception e) {
            log.error("Error getting cache statistics", e)
            return [:]
        }
    }
    
    /**
     * Clear all caches
     */
    void clearAllCaches() {
        try {
            log.info("Clearing all caches...")
            
            cacheManager.cacheNames.each { cacheName ->
                Cache cache = cacheManager.getCache(cacheName)
                cache?.clear()
            }
            
            log.info("All caches cleared")
            
        } catch (Exception e) {
            log.error("Error clearing all caches", e)
        }
    }
    
    /**
     * Scheduled cache warmup (runs every 6 hours)
     */
    // @Scheduled annotation removed for Grails 6.2.3 compatibility - implement with Quartz
    void scheduledCacheWarmup() {
        try {
            log.info("Starting scheduled cache warmup...")
            warmupAllCaches()
        } catch (Exception e) {
            log.error("Scheduled cache warmup failed", e)
        }
    }
    
    /**
     * Scheduled cache cleanup (runs daily at 1 AM)
     */
    // @Scheduled annotation removed for Grails 6.2.3 compatibility - implement with Quartz
    void scheduledCacheCleanup() {
        try {
            log.info("Starting scheduled cache cleanup...")
            
            // Clear and rewarm caches to remove stale entries
            clearAllCaches()
            warmupAllCaches()
            
        } catch (Exception e) {
            log.error("Scheduled cache cleanup failed", e)
        }
    }
}
