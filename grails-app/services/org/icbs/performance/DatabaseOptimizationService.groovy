package org.icbs.performance

import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.springframework.scheduling.annotation.Scheduled
import groovy.sql.Sql
import javax.sql.DataSource

/**
 * Database Optimization Service
 * Manages database performance optimizations, monitoring, and maintenance
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
@Service
@Transactional
@Slf4j
class DatabaseOptimizationService {
    
    DataSource dataSource
    
    /**
     * Refresh materialized views for better query performance
     */
    void refreshMaterializedViews() {
        try {
            log.info("Refreshing materialized views...")
            
            Sql sql = new Sql(dataSource)
            sql.call("SELECT refresh_performance_views()")
            sql.close()
            
            log.info("Materialized views refreshed successfully")
            
        } catch (Exception e) {
            log.error("Error refreshing materialized views", e)
            throw new RuntimeException("Failed to refresh materialized views", e)
        }
    }
    
    /**
     * Update table statistics for query optimizer
     */
    void updateTableStatistics() {
        try {
            log.info("Updating table statistics...")
            
            Sql sql = new Sql(dataSource)
            sql.call("SELECT update_table_statistics()")
            sql.close()
            
            log.info("Table statistics updated successfully")
            
        } catch (Exception e) {
            log.error("Error updating table statistics", e)
            throw new RuntimeException("Failed to update table statistics", e)
        }
    }
    
    /**
     * Get customer balance summary using optimized stored procedure
     */
    Map<String, Object> getCustomerBalanceSummary(Long customerId) {
        try {
            Sql sql = new Sql(dataSource)
            
            def result = sql.firstRow("""
                SELECT * FROM get_customer_balance_summary(?)
            """, [customerId])
            
            sql.close()
            
            return [
                totalDeposits: result?.total_deposits ?: 0,
                totalLoans: result?.total_loans ?: 0,
                netPosition: result?.net_position ?: 0,
                depositCount: result?.deposit_count ?: 0,
                loanCount: result?.loan_count ?: 0
            ]
            
        } catch (Exception e) {
            log.error("Error getting customer balance summary for customer: ${customerId}", e)
            throw new RuntimeException("Failed to get customer balance summary", e)
        }
    }
    
    /**
     * Search transactions with optimized pagination
     */
    List<Map<String, Object>> searchTransactions(Map searchParams) {
        try {
            Sql sql = new Sql(dataSource)
            
            def results = sql.rows("""
                SELECT * FROM search_transactions(?, ?, ?, ?, ?, ?, ?, ?)
            """, [
                searchParams.customerId,
                searchParams.branchId,
                searchParams.startDate,
                searchParams.endDate,
                searchParams.minAmount,
                searchParams.maxAmount,
                searchParams.pageSize ?: 50,
                searchParams.pageOffset ?: 0
            ])
            
            sql.close()
            
            return results.collect { row ->
                [
                    id: row.id,
                    txnDate: row.txn_date,
                    amount: row.amount,
                    description: row.description,
                    customerName: row.customer_name,
                    branchName: row.branch_name
                ]
            }
            
        } catch (Exception e) {
            log.error("Error searching transactions with params: ${searchParams}", e)
            throw new RuntimeException("Failed to search transactions", e)
        }
    }
    
    /**
     * Get customer summary from materialized view
     */
    List<Map<String, Object>> getCustomerSummary(Map filterParams = [:]) {
        try {
            StringBuilder query = new StringBuilder("""
                SELECT 
                    id, customer_id, display_name, branch_name,
                    deposit_count, total_deposits, loan_count, total_loans,
                    date_created, last_updated
                FROM mv_customer_summary
                WHERE 1=1
            """)
            
            List<Object> params = []
            
            if (filterParams.branchId) {
                query.append(" AND branch_id = ?")
                params.add(filterParams.branchId)
            }
            
            if (filterParams.minDeposits) {
                query.append(" AND total_deposits >= ?")
                params.add(filterParams.minDeposits)
            }
            
            if (filterParams.searchName) {
                query.append(" AND display_name ILIKE ?")
                params.add("%${filterParams.searchName}%")
            }
            
            query.append(" ORDER BY total_deposits DESC")
            
            if (filterParams.limit) {
                query.append(" LIMIT ?")
                params.add(filterParams.limit)
            }
            
            Sql sql = new Sql(dataSource)
            def results = sql.rows(query.toString(), params)
            sql.close()
            
            return results.collect { row ->
                [
                    id: row.id,
                    customerId: row.customer_id,
                    displayName: row.display_name,
                    branchName: row.branch_name,
                    depositCount: row.deposit_count,
                    totalDeposits: row.total_deposits,
                    loanCount: row.loan_count,
                    totalLoans: row.total_loans,
                    dateCreated: row.date_created,
                    lastUpdated: row.last_updated
                ]
            }
            
        } catch (Exception e) {
            log.error("Error getting customer summary with params: ${filterParams}", e)
            throw new RuntimeException("Failed to get customer summary", e)
        }
    }
    
    /**
     * Get branch performance from materialized view
     */
    List<Map<String, Object>> getBranchPerformance() {
        try {
            Sql sql = new Sql(dataSource)
            
            def results = sql.rows("""
                SELECT 
                    id, code, name, customer_count, deposit_count, 
                    total_deposits, loan_count, total_loans, report_date
                FROM mv_branch_performance
                ORDER BY total_deposits DESC
            """)
            
            sql.close()
            
            return results.collect { row ->
                [
                    id: row.id,
                    code: row.code,
                    name: row.name,
                    customerCount: row.customer_count,
                    depositCount: row.deposit_count,
                    totalDeposits: row.total_deposits,
                    loanCount: row.loan_count,
                    totalLoans: row.total_loans,
                    reportDate: row.report_date
                ]
            }
            
        } catch (Exception e) {
            log.error("Error getting branch performance", e)
            throw new RuntimeException("Failed to get branch performance", e)
        }
    }
    
    /**
     * Cleanup old audit logs
     */
    int cleanupOldAuditLogs(int retentionDays = 365) {
        try {
            log.info("Cleaning up audit logs older than ${retentionDays} days...")
            
            Sql sql = new Sql(dataSource)
            def result = sql.firstRow("SELECT cleanup_old_audit_logs(?)", [retentionDays])
            sql.close()
            
            int deletedCount = result?.cleanup_old_audit_logs ?: 0
            log.info("Cleaned up ${deletedCount} old audit log entries")
            
            return deletedCount
            
        } catch (Exception e) {
            log.error("Error cleaning up old audit logs", e)
            throw new RuntimeException("Failed to cleanup old audit logs", e)
        }
    }
    
    /**
     * Get database performance metrics
     */
    Map<String, Object> getDatabasePerformanceMetrics() {
        try {
            Sql sql = new Sql(dataSource)
            
            // Get slow queries
            def slowQueries = sql.rows("""
                SELECT query, calls, total_time, mean_time, hit_percent
                FROM v_slow_queries
                LIMIT 10
            """)
            
            // Get table sizes
            def tableSizes = sql.rows("""
                SELECT tablename, size
                FROM v_table_sizes
                LIMIT 20
            """)
            
            // Get database size
            def dbSize = sql.firstRow("""
                SELECT pg_size_pretty(pg_database_size(current_database())) as db_size
            """)
            
            // Get connection stats
            def connectionStats = sql.firstRow("""
                SELECT 
                    count(*) as total_connections,
                    count(*) FILTER (WHERE state = 'active') as active_connections,
                    count(*) FILTER (WHERE state = 'idle') as idle_connections
                FROM pg_stat_activity
                WHERE datname = current_database()
            """)
            
            sql.close()
            
            return [
                slowQueries: slowQueries,
                tableSizes: tableSizes,
                databaseSize: dbSize?.db_size,
                connectionStats: [
                    total: connectionStats?.total_connections ?: 0,
                    active: connectionStats?.active_connections ?: 0,
                    idle: connectionStats?.idle_connections ?: 0
                ]
            ]
            
        } catch (Exception e) {
            log.error("Error getting database performance metrics", e)
            throw new RuntimeException("Failed to get database performance metrics", e)
        }
    }
    
    /**
     * Analyze query performance for specific table
     */
    Map<String, Object> analyzeTablePerformance(String tableName) {
        try {
            Sql sql = new Sql(dataSource)
            
            // Get table statistics
            def tableStats = sql.firstRow("""
                SELECT 
                    schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del,
                    n_live_tup, n_dead_tup, last_vacuum, last_autovacuum,
                    last_analyze, last_autoanalyze
                FROM pg_stat_user_tables
                WHERE tablename = ?
            """, [tableName])
            
            // Get index usage
            def indexUsage = sql.rows("""
                SELECT 
                    indexname, idx_tup_read, idx_tup_fetch,
                    pg_size_pretty(pg_relation_size(indexname::regclass)) as index_size
                FROM pg_stat_user_indexes
                WHERE tablename = ?
                ORDER BY idx_tup_read DESC
            """, [tableName])
            
            // Get table size
            def tableSize = sql.firstRow("""
                SELECT 
                    pg_size_pretty(pg_total_relation_size(?)) as total_size,
                    pg_size_pretty(pg_relation_size(?)) as table_size
            """, [tableName, tableName])
            
            sql.close()
            
            return [
                tableStats: tableStats,
                indexUsage: indexUsage,
                tableSize: tableSize
            ]
            
        } catch (Exception e) {
            log.error("Error analyzing table performance for: ${tableName}", e)
            throw new RuntimeException("Failed to analyze table performance", e)
        }
    }
    
    /**
     * Optimize table by running VACUUM and ANALYZE
     */
    void optimizeTable(String tableName) {
        try {
            log.info("Optimizing table: ${tableName}")
            
            Sql sql = new Sql(dataSource)
            
            // Run VACUUM ANALYZE
            sql.execute("VACUUM ANALYZE ${tableName}".toString())
            
            sql.close()
            
            log.info("Table optimization completed for: ${tableName}")
            
        } catch (Exception e) {
            log.error("Error optimizing table: ${tableName}", e)
            throw new RuntimeException("Failed to optimize table", e)
        }
    }
    
    /**
     * Scheduled task to refresh materialized views (runs every hour)
     */
    @Scheduled(cron = "0 0 * * * *") // Every hour
    void scheduledMaterializedViewRefresh() {
        try {
            refreshMaterializedViews()
        } catch (Exception e) {
            log.error("Scheduled materialized view refresh failed", e)
        }
    }
    
    /**
     * Scheduled task to update statistics (runs daily at 2 AM)
     */
    @Scheduled(cron = "0 0 2 * * *") // Daily at 2 AM
    void scheduledStatisticsUpdate() {
        try {
            updateTableStatistics()
        } catch (Exception e) {
            log.error("Scheduled statistics update failed", e)
        }
    }
    
    /**
     * Scheduled task to cleanup old audit logs (runs weekly on Sunday at 3 AM)
     */
    @Scheduled(cron = "0 0 3 * * 0") // Weekly on Sunday at 3 AM
    void scheduledAuditLogCleanup() {
        try {
            cleanupOldAuditLogs(365) // Keep 1 year of audit logs
        } catch (Exception e) {
            log.error("Scheduled audit log cleanup failed", e)
        }
    }
    
    /**
     * Get optimization recommendations
     */
    List<Map<String, Object>> getOptimizationRecommendations() {
        try {
            List<Map<String, Object>> recommendations = []
            
            Sql sql = new Sql(dataSource)
            
            // Check for tables that need VACUUM
            def vacuumNeeded = sql.rows("""
                SELECT tablename, n_dead_tup, n_live_tup,
                       ROUND(n_dead_tup * 100.0 / NULLIF(n_live_tup + n_dead_tup, 0), 2) as dead_tuple_percent
                FROM pg_stat_user_tables
                WHERE n_dead_tup > 1000 
                AND n_dead_tup * 100.0 / NULLIF(n_live_tup + n_dead_tup, 0) > 10
                ORDER BY dead_tuple_percent DESC
            """)
            
            vacuumNeeded.each { table ->
                recommendations << [
                    type: 'VACUUM_NEEDED',
                    priority: 'HIGH',
                    table: table.tablename,
                    description: "Table ${table.tablename} has ${table.dead_tuple_percent}% dead tuples and needs VACUUM",
                    action: "Run VACUUM ANALYZE on ${table.tablename}"
                ]
            }
            
            // Check for unused indexes
            def unusedIndexes = sql.rows("""
                SELECT schemaname, tablename, indexname, idx_tup_read
                FROM pg_stat_user_indexes
                WHERE idx_tup_read = 0
                AND indexname NOT LIKE '%_pkey'
            """)
            
            unusedIndexes.each { index ->
                recommendations << [
                    type: 'UNUSED_INDEX',
                    priority: 'MEDIUM',
                    table: index.tablename,
                    description: "Index ${index.indexname} on ${index.tablename} is never used",
                    action: "Consider dropping unused index ${index.indexname}"
                ]
            }
            
            sql.close()
            
            return recommendations
            
        } catch (Exception e) {
            log.error("Error getting optimization recommendations", e)
            return []
        }
    }
}
