package org.icbs.security

import io.jsonwebtoken.*
import io.jsonwebtoken.security.Keys
import grails.gorm.transactions.Transactional
import org.springframework.beans.factory.annotation.Value
import groovy.util.logging.Slf4j
import javax.crypto.SecretKey
import java.security.Key

/**
 * JWT Token Service
 * Handles JWT token generation, validation, and management for secure authentication
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
@Service
@Transactional
@Slf4j
class JwtTokenService {
    
    @Value('${jwt.secret:QwikBankaSecureJWTSecretKeyForBankingSystem2025!@#$%^&*()}')
    String jwtSecret
    
    @Value('${jwt.expiration:86400}') // 24 hours in seconds
    int jwtExpirationInSeconds
    
    @Value('${jwt.refresh.expiration:604800}') // 7 days in seconds
    int refreshTokenExpirationInSeconds
    
    @Value('${jwt.issuer:QwikBanka}')
    String jwtIssuer
    
    @Value('${jwt.audience:QwikBanka-Users}')
    String jwtAudience
    
    // Cache for revoked tokens (in production, use Redis)
    private static final Set<String> revokedTokens = Collections.synchronizedSet(new HashSet<String>())
    
    /**
     * Get the signing key for JWT tokens
     */
    private Key getSigningKey() {
        byte[] keyBytes = jwtSecret.getBytes('UTF-8')
        return Keys.hmacShaKeyFor(keyBytes)
    }
    
    /**
     * Generate access token for user
     */
    String generateAccessToken(SecureUser user, String sessionId = null) {
        Date now = new Date()
        Date expiryDate = new Date(now.time + (jwtExpirationInSeconds * 1000L))
        
        Map<String, Object> claims = buildUserClaims(user, sessionId)
        
        try {
            return Jwts.builder()
                .setClaims(claims)
                .setSubject(user.username)
                .setIssuer(jwtIssuer)
                .setAudience(jwtAudience)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .setId(UUID.randomUUID().toString()) // JTI claim
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact()
        } catch (Exception e) {
            log.error("Error generating access token for user: ${user.username}", e)
            throw new SecurityException("Failed to generate access token", e)
        }
    }
    
    /**
     * Generate refresh token for user
     */
    String generateRefreshToken(SecureUser user, String sessionId = null) {
        Date now = new Date()
        Date expiryDate = new Date(now.time + (refreshTokenExpirationInSeconds * 1000L))
        
        Map<String, Object> claims = [
            userId: user.id,
            username: user.username,
            tokenType: 'REFRESH',
            sessionId: sessionId
        ]
        
        try {
            return Jwts.builder()
                .setClaims(claims)
                .setSubject(user.username)
                .setIssuer(jwtIssuer)
                .setAudience(jwtAudience)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .setId(UUID.randomUUID().toString())
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact()
        } catch (Exception e) {
            log.error("Error generating refresh token for user: ${user.username}", e)
            throw new SecurityException("Failed to generate refresh token", e)
        }
    }
    
    /**
     * Build user claims for JWT token
     */
    private Map<String, Object> buildUserClaims(SecureUser user, String sessionId) {
        Map<String, Object> claims = [:]
        
        // Basic user information
        claims.userId = user.id
        claims.username = user.username
        claims.email = user.email
        claims.fullName = user.fullName
        claims.employeeId = user.employeeId
        
        // Branch and department information
        claims.branchId = user.branch?.id
        claims.branchCode = user.branch?.code
        claims.branchName = user.branch?.name
        claims.departmentId = user.department?.id
        claims.departmentCode = user.department?.code
        
        // Security information
        claims.mfaEnabled = user.mfaEnabled
        claims.accountLocked = user.accountLocked
        claims.passwordExpired = user.passwordExpired
        claims.sessionId = sessionId
        
        // Roles and permissions
        claims.roles = user.authorities.collect { it.authority }
        claims.permissions = user.permissionsList
        
        // Token metadata
        claims.tokenType = 'ACCESS'
        claims.securityLevel = determineSecurityLevel(user)
        
        return claims
    }
    
    /**
     * Determine security level for user
     */
    private String determineSecurityLevel(SecureUser user) {
        if (user.hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_SYSTEM_ADMIN')) {
            return 'PRIVILEGED'
        } else if (user.hasAnyRole('ROLE_BRANCH_MANAGER', 'ROLE_OPERATIONS_MANAGER', 'ROLE_AUDITOR')) {
            return 'ELEVATED'
        } else {
            return 'STANDARD'
        }
    }
    
    /**
     * Validate JWT token
     */
    boolean validateToken(String token) {
        try {
            // Check if token is revoked
            if (isTokenRevoked(token)) {
                log.warn("Attempt to use revoked token")
                return false
            }
            
            // Parse and validate token
            Claims claims = getClaimsFromToken(token)
            
            // Additional validation
            if (!claims.issuer?.equals(jwtIssuer)) {
                log.warn("Invalid token issuer: ${claims.issuer}")
                return false
            }
            
            if (!claims.audience?.equals(jwtAudience)) {
                log.warn("Invalid token audience: ${claims.audience}")
                return false
            }
            
            // Check if user still exists and is active
            String username = claims.subject
            SecureUser user = SecureUser.findByUsername(username)
            if (!user || !user.enabled || user.accountLocked) {
                log.warn("Token validation failed - user not found or inactive: ${username}")
                return false
            }
            
            return true
            
        } catch (ExpiredJwtException e) {
            log.debug("Token expired: ${e.message}")
            return false
        } catch (UnsupportedJwtException e) {
            log.warn("Unsupported JWT token: ${e.message}")
            return false
        } catch (MalformedJwtException e) {
            log.warn("Malformed JWT token: ${e.message}")
            return false
        } catch (SignatureException e) {
            log.warn("Invalid JWT signature: ${e.message}")
            return false
        } catch (IllegalArgumentException e) {
            log.warn("JWT token compact of handler are invalid: ${e.message}")
            return false
        } catch (Exception e) {
            log.error("JWT token validation error", e)
            return false
        }
    }
    
    /**
     * Get claims from JWT token
     */
    Claims getClaimsFromToken(String token) {
        try {
            return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .requireIssuer(jwtIssuer)
                .requireAudience(jwtAudience)
                .build()
                .parseClaimsJws(token)
                .body
        } catch (Exception e) {
            log.error("Error parsing JWT token claims", e)
            throw e
        }
    }
    
    /**
     * Get username from JWT token
     */
    String getUsernameFromToken(String token) {
        try {
            return getClaimsFromToken(token).subject
        } catch (Exception e) {
            log.error("Error extracting username from token", e)
            return null
        }
    }
    
    /**
     * Get user ID from JWT token
     */
    Long getUserIdFromToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token)
            return claims.get('userId', Long.class)
        } catch (Exception e) {
            log.error("Error extracting user ID from token", e)
            return null
        }
    }
    
    /**
     * Get session ID from JWT token
     */
    String getSessionIdFromToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token)
            return claims.get('sessionId', String.class)
        } catch (Exception e) {
            log.error("Error extracting session ID from token", e)
            return null
        }
    }
    
    /**
     * Get expiration date from JWT token
     */
    Date getExpirationDateFromToken(String token) {
        try {
            return getClaimsFromToken(token).expiration
        } catch (Exception e) {
            log.error("Error extracting expiration date from token", e)
            return null
        }
    }
    
    /**
     * Check if JWT token is expired
     */
    boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token)
            return expiration?.before(new Date()) ?: true
        } catch (Exception e) {
            log.error("Error checking token expiration", e)
            return true
        }
    }
    
    /**
     * Get remaining time until token expires (in seconds)
     */
    long getRemainingTimeSeconds(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token)
            if (!expiration) return 0
            
            long remaining = expiration.time - new Date().time
            return Math.max(0, remaining / 1000)
        } catch (Exception e) {
            log.error("Error calculating remaining time", e)
            return 0
        }
    }
    
    /**
     * Refresh access token using refresh token
     */
    String refreshAccessToken(String refreshToken) {
        try {
            if (!validateToken(refreshToken)) {
                throw new SecurityException("Invalid refresh token")
            }
            
            Claims claims = getClaimsFromToken(refreshToken)
            String tokenType = claims.get('tokenType', String.class)
            
            if (!'REFRESH'.equals(tokenType)) {
                throw new SecurityException("Token is not a refresh token")
            }
            
            String username = claims.subject
            SecureUser user = SecureUser.findByUsername(username)
            
            if (!user || !user.enabled || user.accountLocked) {
                throw new SecurityException("User not found or inactive")
            }
            
            String sessionId = claims.get('sessionId', String.class)
            return generateAccessToken(user, sessionId)
            
        } catch (Exception e) {
            log.error("Error refreshing access token", e)
            throw new SecurityException("Failed to refresh access token", e)
        }
    }
    
    /**
     * Revoke JWT token
     */
    void revokeToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token)
            String jti = claims.id
            
            if (jti) {
                revokedTokens.add(jti)
                log.info("Token revoked: ${jti}")
                
                // Create audit log
                SecurityAuditLog.createAuditLog([
                    eventType: 'TOKEN_REVOKED',
                    eventCategory: 'SECURITY',
                    eventDescription: "JWT token revoked",
                    username: claims.subject,
                    result: 'SUCCESS',
                    eventData: [tokenId: jti]
                ])
            }
        } catch (Exception e) {
            log.error("Error revoking token", e)
        }
    }
    
    /**
     * Check if token is revoked
     */
    boolean isTokenRevoked(String token) {
        try {
            Claims claims = getClaimsFromToken(token)
            String jti = claims.id
            return jti && revokedTokens.contains(jti)
        } catch (Exception e) {
            log.error("Error checking token revocation status", e)
            return true // Assume revoked if we can't check
        }
    }
    
    /**
     * Revoke all tokens for user
     */
    void revokeAllTokensForUser(String username) {
        // In a production system, you would query a token blacklist database
        // For now, we'll just log the action
        log.info("All tokens revoked for user: ${username}")
        
        SecurityAuditLog.createAuditLog([
            eventType: 'ALL_TOKENS_REVOKED',
            eventCategory: 'SECURITY',
            eventDescription: "All JWT tokens revoked for user",
            username: username,
            result: 'SUCCESS'
        ])
    }
    
    /**
     * Generate token pair (access + refresh)
     */
    Map<String, String> generateTokenPair(SecureUser user, String sessionId = null) {
        String accessToken = generateAccessToken(user, sessionId)
        String refreshToken = generateRefreshToken(user, sessionId)
        
        return [
            accessToken: accessToken,
            refreshToken: refreshToken,
            tokenType: 'Bearer',
            expiresIn: jwtExpirationInSeconds
        ]
    }
    
    /**
     * Extract token from Authorization header
     */
    String extractTokenFromHeader(String authorizationHeader) {
        if (authorizationHeader && authorizationHeader.startsWith('Bearer ')) {
            return authorizationHeader.substring(7)
        }
        return null
    }
    
    /**
     * Get token information
     */
    Map<String, Object> getTokenInfo(String token) {
        try {
            Claims claims = getClaimsFromToken(token)
            
            return [
                subject: claims.subject,
                issuer: claims.issuer,
                audience: claims.audience,
                issuedAt: claims.issuedAt,
                expiration: claims.expiration,
                tokenId: claims.id,
                userId: claims.get('userId'),
                username: claims.get('username'),
                roles: claims.get('roles'),
                permissions: claims.get('permissions'),
                securityLevel: claims.get('securityLevel'),
                tokenType: claims.get('tokenType'),
                sessionId: claims.get('sessionId'),
                remainingSeconds: getRemainingTimeSeconds(token),
                isExpired: isTokenExpired(token),
                isRevoked: isTokenRevoked(token)
            ]
        } catch (Exception e) {
            log.error("Error getting token information", e)
            return [error: "Invalid token"]
        }
    }
    
    /**
     * Cleanup expired revoked tokens
     */
    void cleanupRevokedTokens() {
        // In production, implement proper cleanup of expired tokens from database
        log.info("Cleaned up revoked tokens cache")
    }
}
