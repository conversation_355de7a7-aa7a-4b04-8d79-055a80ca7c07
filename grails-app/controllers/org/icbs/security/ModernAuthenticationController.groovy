package org.icbs.security

import grails.converters.JSON
import grails.gorm.transactions.Transactional
import groovy.util.logging.Slf4j
import org.springframework.http.HttpStatus
import org.icbs.admin.UserMaster

/**
 * Modern Authentication Controller
 * Handles both traditional web authentication and modern JWT-based API authentication
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
@Slf4j
class ModernAuthenticationController {
    
    JwtTokenService jwtTokenService
    EncryptionService encryptionService
    SecurityAuditService securityAuditService
    
    static allowedMethods = [
        authenticate: 'POST',
        login: 'GET',
        logout: 'POST',
        refreshToken: 'POST',
        changePassword: 'POST',
        forgotPassword: 'POST',
        resetPassword: 'POST'
    ]
    
    /**
     * Display login page
     */
    def login() {
        // If user is already authenticated, redirect to dashboard
        if (session.currentUser) {
            redirect(controller: 'dashboard', action: 'index')
            return
        }
        
        // Check for any security messages
        def securityMessage = flash.securityMessage
        def errorMessage = flash.errorMessage
        
        [securityMessage: securityMessage, errorMessage: errorMessage]
    }
    
    /**
     * Authenticate user (supports both web and API authentication)
     */
    @Transactional
    def authenticate() {
        String username = params.username?.trim()
        String password = params.password
        String ipAddress = request.remoteAddr
        String userAgent = request.getHeader('User-Agent')
        boolean isApiRequest = isApiRequest()
        
        // Validate input
        if (!username || !password) {
            String message = "Username and password are required"
            
            securityAuditService.logAuthenticationEvent(
                'LOGIN_FAILURE', username ?: 'UNKNOWN', false,
                [reason: 'Missing credentials', ipAddress: ipAddress]
            )
            
            if (isApiRequest) {
                renderErrorJson(HttpStatus.BAD_REQUEST, message)
            } else {
                flash.errorMessage = message
                redirect(action: 'login')
            }
            return
        }
        
        try {
            // Find user by username (check both SecureUser and legacy UserMaster)
            SecureUser secureUser = SecureUser.findByUsername(username)
            UserMaster legacyUser = null
            
            if (!secureUser) {
                // Check legacy user table for backward compatibility
                legacyUser = UserMaster.findByUsername(username)
                if (legacyUser) {
                    // Migrate legacy user to new security system
                    secureUser = migrateLegacyUser(legacyUser)
                }
            }
            
            if (!secureUser) {
                handleAuthenticationFailure(username, "User not found", ipAddress, isApiRequest)
                return
            }
            
            // Check account status
            if (!secureUser.enabled) {
                handleAuthenticationFailure(username, "Account is disabled", ipAddress, isApiRequest)
                return
            }
            
            if (secureUser.accountLocked) {
                handleAuthenticationFailure(username, "Account is locked", ipAddress, isApiRequest)
                return
            }
            
            if (secureUser.isAccountLockedByFailedAttempts()) {
                handleAuthenticationFailure(username, "Account locked due to failed login attempts", ipAddress, isApiRequest)
                return
            }
            
            // Verify password
            boolean passwordValid = false
            if (secureUser.password.startsWith('$2a$') || secureUser.password.startsWith('$2b$')) {
                // BCrypt password
                passwordValid = encryptionService.verifyPassword(password, secureUser.password)
            } else {
                // Legacy MD5 password - verify and migrate
                passwordValid = verifyAndMigrateLegacyPassword(secureUser, password)
            }
            
            if (!passwordValid) {
                handleAuthenticationFailure(username, "Invalid password", ipAddress, isApiRequest, secureUser)
                return
            }
            
            // Check if password is expired
            if (secureUser.isPasswordExpiredByPolicy()) {
                if (isApiRequest) {
                    renderErrorJson(HttpStatus.FORBIDDEN, "Password has expired and must be changed")
                } else {
                    session.passwordChangeRequired = true
                    session.tempUser = username
                    redirect(controller: 'user', action: 'changePassword')
                }
                return
            }
            
            // Authentication successful
            handleAuthenticationSuccess(secureUser, ipAddress, userAgent, isApiRequest)
            
        } catch (Exception e) {
            log.error("Authentication error for user: ${username}", e)
            
            securityAuditService.logSecurityEvent([
                eventType: 'AUTHENTICATION_ERROR',
                eventDescription: "Authentication system error: ${e.message}",
                username: username,
                ipAddress: ipAddress,
                result: 'ERROR',
                errorMessage: e.message
            ])
            
            if (isApiRequest) {
                renderErrorJson(HttpStatus.INTERNAL_SERVER_ERROR, "Authentication system error")
            } else {
                flash.errorMessage = "System error during authentication. Please try again."
                redirect(action: 'login')
            }
        }
    }
    
    /**
     * Handle successful authentication
     */
    private void handleAuthenticationSuccess(SecureUser user, String ipAddress, String userAgent, boolean isApiRequest) {
        String sessionId = UUID.randomUUID().toString()
        
        // Record successful login
        user.recordSuccessfulLogin(ipAddress, sessionId)
        user.save(flush: true)
        
        // Create user session
        UserSession userSession = UserSession.createSession(user.userMaster, sessionId, ipAddress, userAgent)
        
        // Log successful authentication
        securityAuditService.logAuthenticationEvent(
            'LOGIN_SUCCESS', user.username, true,
            [sessionId: sessionId, ipAddress: ipAddress, userAgent: userAgent]
        )
        
        if (isApiRequest) {
            // Generate JWT tokens for API access
            Map tokenPair = jwtTokenService.generateTokenPair(user, sessionId)
            
            render(contentType: 'application/json') {
                [
                    success: true,
                    message: 'Authentication successful',
                    user: [
                        id: user.id,
                        username: user.username,
                        email: user.email,
                        fullName: user.fullName,
                        branch: user.branch?.name,
                        department: user.department?.name,
                        roles: user.authorities.collect { it.authority }
                    ],
                    tokens: tokenPair,
                    sessionId: sessionId
                ]
            }
        } else {
            // Set session for web access
            session.currentUser = user.username
            session.currentUserId = user.id
            session.sessionId = sessionId
            session.userBranch = user.branch?.code
            session.userDepartment = user.department?.code
            session.userRoles = user.authorities.collect { it.authority }
            
            // Redirect to original URL or dashboard
            String originalUrl = session.originalUrl
            session.removeAttribute('originalUrl')
            
            if (originalUrl) {
                redirect(url: originalUrl)
            } else {
                redirect(controller: 'dashboard', action: 'index')
            }
        }
    }
    
    /**
     * Handle authentication failure
     */
    private void handleAuthenticationFailure(String username, String reason, String ipAddress, boolean isApiRequest, SecureUser user = null) {
        // Record failed login attempt
        if (user) {
            user.recordFailedLogin(ipAddress)
            user.save(flush: true)
        }
        
        // Log failed authentication
        securityAuditService.logAuthenticationEvent(
            'LOGIN_FAILURE', username, false,
            [reason: reason, ipAddress: ipAddress]
        )
        
        if (isApiRequest) {
            renderErrorJson(HttpStatus.UNAUTHORIZED, "Authentication failed: ${reason}")
        } else {
            flash.errorMessage = "Authentication failed. Please check your credentials."
            redirect(action: 'login')
        }
    }
    
    /**
     * Logout user
     */
    @Transactional
    def logout() {
        String username = session.currentUser ?: request.getAttribute('currentUser')
        String sessionId = session.sessionId ?: request.getAttribute('currentSessionId')
        boolean isApiRequest = isApiRequest()
        
        if (username) {
            // Terminate user session
            if (sessionId) {
                UserSession userSession = UserSession.findBySessionId(sessionId)
                if (userSession) {
                    userSession.terminate('USER_LOGOUT')
                    userSession.save()
                }
            }
            
            // Revoke JWT tokens if API request
            if (isApiRequest) {
                String authHeader = request.getHeader('Authorization')
                String token = jwtTokenService.extractTokenFromHeader(authHeader)
                if (token) {
                    jwtTokenService.revokeToken(token)
                }
            }
            
            // Log logout event
            securityAuditService.logAuthenticationEvent('LOGOUT', username, true, [sessionId: sessionId])
        }
        
        // Clear session
        session.invalidate()
        
        if (isApiRequest) {
            render(contentType: 'application/json') {
                [success: true, message: 'Logout successful']
            }
        } else {
            redirect(action: 'login')
        }
    }
    
    /**
     * Refresh JWT token
     */
    def refreshToken() {
        if (!isApiRequest()) {
            renderErrorJson(HttpStatus.BAD_REQUEST, "This endpoint is only available for API requests")
            return
        }
        
        String refreshToken = request.JSON?.refreshToken ?: params.refreshToken
        
        if (!refreshToken) {
            renderErrorJson(HttpStatus.BAD_REQUEST, "Refresh token is required")
            return
        }
        
        try {
            String newAccessToken = jwtTokenService.refreshAccessToken(refreshToken)
            
            render(contentType: 'application/json') {
                [
                    success: true,
                    accessToken: newAccessToken,
                    tokenType: 'Bearer',
                    expiresIn: jwtTokenService.jwtExpirationInSeconds
                ]
            }
            
        } catch (Exception e) {
            log.error("Token refresh error", e)
            renderErrorJson(HttpStatus.UNAUTHORIZED, "Invalid or expired refresh token")
        }
    }
    
    /**
     * Verify and migrate legacy MD5 password to BCrypt
     */
    private boolean verifyAndMigrateLegacyPassword(SecureUser user, String plainPassword) {
        // Verify MD5 password (legacy)
        String md5Hash = plainPassword.encodeAsMD5()
        boolean isValid = user.password == md5Hash
        
        if (isValid) {
            // Migrate to BCrypt
            String bcryptHash = encryptionService.hashPassword(plainPassword)
            user.updatePassword(bcryptHash)
            user.save(flush: true)
            
            log.info("Migrated password from MD5 to BCrypt for user: ${user.username}")
            
            securityAuditService.logSecurityEvent([
                eventType: 'PASSWORD_MIGRATION',
                eventDescription: "Password migrated from MD5 to BCrypt",
                username: user.username,
                result: 'SUCCESS'
            ])
        }
        
        return isValid
    }
    
    /**
     * Migrate legacy UserMaster to SecureUser
     */
    private SecureUser migrateLegacyUser(UserMaster legacyUser) {
        try {
            SecureUser secureUser = new SecureUser(
                username: legacyUser.username,
                password: legacyUser.password, // Will be migrated on first login
                email: legacyUser.email ?: "${legacyUser.username}@qwikbanka.com",
                firstName: legacyUser.firstName ?: 'Unknown',
                lastName: legacyUser.lastName ?: 'User',
                employeeId: legacyUser.employeeId,
                branch: legacyUser.branch,
                department: legacyUser.department,
                enabled: legacyUser.enabled ?: true
            )
            
            secureUser.save(flush: true)
            
            log.info("Migrated legacy user to secure user: ${legacyUser.username}")
            
            securityAuditService.logSecurityEvent([
                eventType: 'USER_MIGRATION',
                eventDescription: "Legacy user migrated to secure user system",
                username: legacyUser.username,
                result: 'SUCCESS'
            ])
            
            return secureUser
            
        } catch (Exception e) {
            log.error("Error migrating legacy user: ${legacyUser.username}", e)
            return null
        }
    }
    
    /**
     * Check if request is an API request
     */
    private boolean isApiRequest() {
        String contentType = request.contentType?.toLowerCase()
        String accept = request.getHeader('Accept')?.toLowerCase()
        
        return contentType?.contains('application/json') ||
               accept?.contains('application/json') ||
               request.getHeader('X-Requested-With') == 'XMLHttpRequest'
    }
    
    /**
     * Render error JSON response
     */
    private void renderErrorJson(HttpStatus status, String message) {
        response.status = status.value()
        render(contentType: 'application/json') {
            [
                success: false,
                error: status.reasonPhrase,
                message: message,
                status: status.value(),
                timestamp: new Date().format("yyyy-MM-dd'T'HH:mm:ss.SSSZ")
            ]
        }
    }
}
