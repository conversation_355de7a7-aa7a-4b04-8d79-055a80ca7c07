import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.cache.CacheManager
import org.springframework.cache.annotation.EnableCaching
import org.springframework.cache.caffeine.CaffeineCacheManager
import com.github.benmanes.caffeine.cache.Caffeine
import java.time.Duration

@Configuration
@EnableCaching
class CacheConfig {

    /**
     * PERFORMANCE OPTIMIZATION: Primary cache manager using Caffeine for high performance
     */
    @Bean
    @Primary
    CacheManager primaryCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager()
        
        // Configure Caffeine cache with optimal settings
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(10000)                    // Maximum 10,000 entries
            .expireAfterWrite(Duration.ofMinutes(30))  // Expire after 30 minutes
            .expireAfterAccess(Duration.ofMinutes(15)) // Expire after 15 minutes of no access
            .recordStats()                         // Enable statistics
        )
        
        // Pre-configure cache names for better performance
        cacheManager.setCacheNames([
            // Customer caches
            'customers',
            'customer',
            'customerBasic',
            'customerSearch',
            'customerSummary',
            'customersByBranch',
            'activeCustomerCount',
            'customerStats',
            
            // Deposit caches
            'deposits',
            'depositByAcctNo',
            'depositSummary',
            'depositSearch',
            'customerDeposits',
            'branchDepositStats',
            'depositsMaturing',
            'dormantAccounts',
            'lowBalanceAccounts',
            
            // Loan caches
            'loans',
            'loanByAcctNo',
            'loanSummary',
            'loanSearch',
            'customerLoans',
            'branchLoanStats',
            'loansMaturing',
            'overdueLoans',
            
            // System caches
            'branches',
            'products',
            'lovItems',
            'configurations',
            'userSessions',
            'glAccounts',

            // Security caches
            'customerCache',
            'depositCache',
            'loanCache',
            'branchCache',
            'lookupCache',
            'summaryCache',
            'securityCache',
            'auditCache',

            // Report caches
            'reportData',
            'reportTemplates'
        ])
        
        return cacheManager
    }

    /**
     * PERFORMANCE OPTIMIZATION: Specialized cache for frequently accessed lookup data
     */
    @Bean('lookupCacheManager')
    CacheManager lookupCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager()
        
        // Longer expiration for relatively static lookup data
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(5000)
            .expireAfterWrite(Duration.ofHours(2))     // Expire after 2 hours
            .expireAfterAccess(Duration.ofHours(1))    // Expire after 1 hour of no access
            .recordStats()
        )
        
        cacheManager.setCacheNames([
            'branches',
            'products',
            'lovItems',
            'currencies',
            'holidays',
            'configurations'
        ])
        
        return cacheManager
    }

    /**
     * PERFORMANCE OPTIMIZATION: Session cache for user-specific data
     */
    @Bean('sessionCacheManager')
    CacheManager sessionCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager()
        
        // Shorter expiration for session-specific data
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofMinutes(20))  // Expire after 20 minutes
            .expireAfterAccess(Duration.ofMinutes(10)) // Expire after 10 minutes of no access
            .recordStats()
        )
        
        cacheManager.setCacheNames([
            'userSessions',
            'userPermissions',
            'userPreferences',
            'userBranches'
        ])
        
        return cacheManager
    }

    /**
     * PERFORMANCE OPTIMIZATION: Large data cache for reports and aggregations
     */
    @Bean('largeCacheManager')
    CacheManager largeCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager()
        
        // Larger capacity but shorter expiration for large data sets
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(500)                      // Fewer entries due to large size
            .expireAfterWrite(Duration.ofMinutes(10))  // Expire after 10 minutes
            .recordStats()
        )
        
        cacheManager.setCacheNames([
            'reportData',
            'dashboardData',
            'statisticsData',
            'aggregatedData'
        ])
        
        return cacheManager
    }
}
