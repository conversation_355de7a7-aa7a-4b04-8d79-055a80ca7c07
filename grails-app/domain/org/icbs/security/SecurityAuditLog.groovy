package org.icbs.security

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString

/**
 * Security Audit Log Domain Class
 * Comprehensive audit trail for all security-related events
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
@Entity
@Table(name = 'security_audit_log')
@EqualsAndHashCode(includes=['id'])
@ToString(includes=['eventType', 'username', 'timestamp'], includeNames=true, includePackage=false)
class SecurityAuditLog implements Serializable {
    
    private static final long serialVersionUID = 1
    
    // Event identification
    String eventId = UUID.randomUUID().toString()
    String eventType
    String eventCategory
    String eventDescription
    
    // User information
    String username
    String userId
    String sessionId
    String userAgent
    
    // Network information
    String ipAddress
    String remoteHost
    String forwardedFor
    
    // Request information
    String requestUri
    String requestMethod
    String requestParameters
    String referer
    
    // Security context
    String securityLevel
    String riskScore
    String threatIndicators
    
    // Event details
    Map<String, Object> eventData = [:]
    String beforeState
    String afterState
    
    // Timing information
    Date timestamp = new Date()
    Long processingTime
    
    // Result information
    String result
    String resultCode
    String errorMessage
    
    // Compliance and regulatory
    String complianceFlags
    String regulatoryCategory
    boolean requiresReview = false
    
    // Relationships
    SecureUser user
    
    static constraints = {
        // Event identification constraints
        eventId nullable: false, unique: true, maxSize: 36
        eventType nullable: false, maxSize: 50,
                 inList: [
                     'LOGIN_SUCCESS',
                     'LOGIN_FAILURE',
                     'LOGOUT',
                     'PASSWORD_CHANGE',
                     'PASSWORD_RESET',
                     'ACCOUNT_LOCKED',
                     'ACCOUNT_UNLOCKED',
                     'ROLE_ASSIGNED',
                     'ROLE_REMOVED',
                     'PERMISSION_GRANTED',
                     'PERMISSION_REVOKED',
                     'MFA_ENABLED',
                     'MFA_DISABLED',
                     'MFA_SUCCESS',
                     'MFA_FAILURE',
                     'SESSION_CREATED',
                     'SESSION_EXPIRED',
                     'SESSION_TERMINATED',
                     'UNAUTHORIZED_ACCESS',
                     'PRIVILEGE_ESCALATION',
                     'DATA_ACCESS',
                     'DATA_MODIFICATION',
                     'DATA_DELETION',
                     'CONFIGURATION_CHANGE',
                     'SECURITY_VIOLATION',
                     'COMPLIANCE_EVENT',
                     'SYSTEM_EVENT',
                     'ERROR_EVENT'
                 ]
        eventCategory nullable: true, maxSize: 50,
                     inList: [
                         'AUTHENTICATION',
                         'AUTHORIZATION',
                         'DATA_ACCESS',
                         'CONFIGURATION',
                         'COMPLIANCE',
                         'SECURITY',
                         'SYSTEM',
                         'ERROR'
                     ]
        eventDescription nullable: false, maxSize: 500
        
        // User information constraints
        username nullable: true, maxSize: 50
        userId nullable: true, maxSize: 50
        sessionId nullable: true, maxSize: 128
        userAgent nullable: true, maxSize: 500
        
        // Network information constraints
        ipAddress nullable: true, maxSize: 45, // IPv6 compatible
                 validator: { val, obj ->
                     if (val && !val.matches(/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$|^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/)) {
                         return 'invalid.ip.address'
                     }
                 }
        remoteHost nullable: true, maxSize: 255
        forwardedFor nullable: true, maxSize: 255
        
        // Request information constraints
        requestUri nullable: true, maxSize: 500
        requestMethod nullable: true, maxSize: 10,
                     inList: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']
        requestParameters nullable: true, maxSize: 2000
        referer nullable: true, maxSize: 500
        
        // Security context constraints
        securityLevel nullable: true, maxSize: 20,
                     inList: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']
        riskScore nullable: true, maxSize: 10,
                 matches: /^\d+(\.\d+)?$/
        threatIndicators nullable: true, maxSize: 500
        
        // Event details constraints
        eventData nullable: true
        beforeState nullable: true, maxSize: 5000
        afterState nullable: true, maxSize: 5000
        
        // Timing constraints
        timestamp nullable: false
        processingTime nullable: true, min: 0L
        
        // Result constraints
        result nullable: true, maxSize: 20,
              inList: ['SUCCESS', 'FAILURE', 'ERROR', 'WARNING', 'BLOCKED']
        resultCode nullable: true, maxSize: 20
        errorMessage nullable: true, maxSize: 1000
        
        // Compliance constraints
        complianceFlags nullable: true, maxSize: 200
        regulatoryCategory nullable: true, maxSize: 50,
                          inList: [
                              'PCI_DSS',
                              'SOX',
                              'GDPR',
                              'AML',
                              'KYC',
                              'BASEL_III',
                              'FFIEC',
                              'GLBA'
                          ]
        
        // Relationship constraints
        user nullable: true
    }
    
    static mapping = {
        table 'security_audit_log'
        id generator: 'identity'
        
        // Large text fields
        eventData type: 'jsonb'
        beforeState type: 'text'
        afterState type: 'text'
        requestParameters type: 'text'
        
        // Performance indexes
        eventType index: 'idx_audit_event_type'
        eventCategory index: 'idx_audit_event_category'
        username index: 'idx_audit_username'
        timestamp index: 'idx_audit_timestamp'
        ipAddress index: 'idx_audit_ip_address'
        result index: 'idx_audit_result'
        securityLevel index: 'idx_audit_security_level'
        requiresReview index: 'idx_audit_requires_review'
        
        // Composite indexes for common queries
        indexes = [
            [name: 'idx_audit_user_time', columnNames: ['username', 'timestamp']],
            [name: 'idx_audit_type_time', columnNames: ['event_type', 'timestamp']],
            [name: 'idx_audit_security_time', columnNames: ['security_level', 'timestamp']],
            [name: 'idx_audit_compliance', columnNames: ['regulatory_category', 'requires_review']],
            [name: 'idx_audit_session', columnNames: ['session_id', 'timestamp']],
            [name: 'idx_audit_ip_time', columnNames: ['ip_address', 'timestamp']]
        ]
        
        // Partitioning by date for large datasets
        tablePerHierarchy false
        
        version false
    }
    
    /**
     * Get event severity level
     */
    String getSeverityLevel() {
        switch (eventType) {
            case ['SECURITY_VIOLATION', 'UNAUTHORIZED_ACCESS', 'PRIVILEGE_ESCALATION']:
                return 'CRITICAL'
            case ['LOGIN_FAILURE', 'ACCOUNT_LOCKED', 'MFA_FAILURE']:
                return 'HIGH'
            case ['PASSWORD_CHANGE', 'ROLE_ASSIGNED', 'CONFIGURATION_CHANGE']:
                return 'MEDIUM'
            case ['LOGIN_SUCCESS', 'LOGOUT', 'DATA_ACCESS']:
                return 'LOW'
            default:
                return 'MEDIUM'
        }
    }
    
    /**
     * Get event color for UI
     */
    String getEventColor() {
        switch (getSeverityLevel()) {
            case 'CRITICAL':
                return '#dc3545' // Red
            case 'HIGH':
                return '#fd7e14' // Orange
            case 'MEDIUM':
                return '#ffc107' // Yellow
            case 'LOW':
                return '#28a745' // Green
            default:
                return '#6c757d' // Gray
        }
    }
    
    /**
     * Check if event is security-related
     */
    boolean isSecurityEvent() {
        return eventCategory in ['AUTHENTICATION', 'AUTHORIZATION', 'SECURITY'] ||
               eventType in ['SECURITY_VIOLATION', 'UNAUTHORIZED_ACCESS', 'PRIVILEGE_ESCALATION']
    }
    
    /**
     * Check if event requires immediate attention
     */
    boolean requiresImmediateAttention() {
        return getSeverityLevel() in ['CRITICAL', 'HIGH'] || requiresReview
    }
    
    /**
     * Get formatted timestamp
     */
    String getFormattedTimestamp() {
        return timestamp.format('yyyy-MM-dd HH:mm:ss')
    }
    
    /**
     * Get processing time in milliseconds
     */
    String getFormattedProcessingTime() {
        if (!processingTime) return 'N/A'
        return "${processingTime}ms"
    }
    
    /**
     * Add event data
     */
    void addEventData(String key, Object value) {
        if (!eventData) {
            eventData = [:]
        }
        eventData[key] = value
    }
    
    /**
     * Get event data value
     */
    Object getEventDataValue(String key) {
        return eventData?.get(key)
    }
    
    /**
     * Set compliance flag
     */
    void setComplianceFlag(String flag) {
        if (!complianceFlags) {
            complianceFlags = flag
        } else {
            complianceFlags += ",${flag}"
        }
    }
    
    /**
     * Check if has compliance flag
     */
    boolean hasComplianceFlag(String flag) {
        return complianceFlags?.contains(flag) ?: false
    }
    
    /**
     * Create audit log entry
     */
    static SecurityAuditLog createAuditLog(Map params) {
        def auditLog = new SecurityAuditLog()
        
        // Set basic properties
        auditLog.eventType = params.eventType
        auditLog.eventCategory = params.eventCategory ?: getDefaultCategory(params.eventType)
        auditLog.eventDescription = params.eventDescription
        auditLog.username = params.username
        auditLog.userId = params.userId
        auditLog.sessionId = params.sessionId
        auditLog.ipAddress = params.ipAddress
        auditLog.userAgent = params.userAgent
        auditLog.requestUri = params.requestUri
        auditLog.requestMethod = params.requestMethod
        auditLog.result = params.result
        auditLog.resultCode = params.resultCode
        auditLog.errorMessage = params.errorMessage
        auditLog.securityLevel = params.securityLevel ?: auditLog.getSeverityLevel()
        auditLog.eventData = params.eventData ?: [:]
        auditLog.beforeState = params.beforeState
        auditLog.afterState = params.afterState
        auditLog.processingTime = params.processingTime
        auditLog.user = params.user
        
        // Set compliance information
        if (params.regulatoryCategory) {
            auditLog.regulatoryCategory = params.regulatoryCategory
        }
        if (params.complianceFlags) {
            auditLog.complianceFlags = params.complianceFlags
        }
        if (params.requiresReview) {
            auditLog.requiresReview = params.requiresReview
        }
        
        auditLog.save(failOnError: true)
        return auditLog
    }
    
    /**
     * Get default category for event type
     */
    private static String getDefaultCategory(String eventType) {
        switch (eventType) {
            case ['LOGIN_SUCCESS', 'LOGIN_FAILURE', 'LOGOUT', 'PASSWORD_CHANGE', 'PASSWORD_RESET']:
                return 'AUTHENTICATION'
            case ['ROLE_ASSIGNED', 'ROLE_REMOVED', 'PERMISSION_GRANTED', 'PERMISSION_REVOKED']:
                return 'AUTHORIZATION'
            case ['DATA_ACCESS', 'DATA_MODIFICATION', 'DATA_DELETION']:
                return 'DATA_ACCESS'
            case ['CONFIGURATION_CHANGE']:
                return 'CONFIGURATION'
            case ['SECURITY_VIOLATION', 'UNAUTHORIZED_ACCESS']:
                return 'SECURITY'
            case ['COMPLIANCE_EVENT']:
                return 'COMPLIANCE'
            default:
                return 'SYSTEM'
        }
    }
    
    /**
     * Get audit statistics
     */
    static Map getAuditStatistics(Date startDate = null, Date endDate = null) {
        def criteria = SecurityAuditLog.createCriteria()
        
        return criteria.get {
            if (startDate) {
                ge('timestamp', startDate)
            }
            if (endDate) {
                le('timestamp', endDate)
            }
            
            projections {
                count('id', 'totalEvents')
                countDistinct('username', 'uniqueUsers')
                countDistinct('ipAddress', 'uniqueIPs')
                countDistinct('sessionId', 'uniqueSessions')
            }
        }
    }
    
    /**
     * Get events by security level
     */
    static List<SecurityAuditLog> findBySecurityLevel(String level, int max = 100) {
        return SecurityAuditLog.findAllBySecurityLevel(level, [max: max, sort: 'timestamp', order: 'desc'])
    }
    
    /**
     * Get recent security events
     */
    static List<SecurityAuditLog> getRecentSecurityEvents(int hours = 24, int max = 100) {
        Date since = new Date() - hours/24
        return SecurityAuditLog.createCriteria().list(max: max) {
            ge('timestamp', since)
            eq('eventCategory', 'SECURITY')
            order('timestamp', 'desc')
        }
    }
    
    /**
     * Get events requiring review
     */
    static List<SecurityAuditLog> getEventsRequiringReview(int max = 100) {
        return SecurityAuditLog.findAllByRequiresReview(true, [max: max, sort: 'timestamp', order: 'desc'])
    }
}
