package org.icbs.security

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import org.apache.commons.lang.builder.HashCodeBuilder

/**
 * Spring Security User-Role Join Domain Class
 * Manages many-to-many relationship between users and roles
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
@Entity
@Table(name = 'secure_user_role')
@EqualsAndHashCode(includes=['secureUser', 'secureRole'])
@ToString(includes=['secureUser', 'secureRole'], includeNames=true, includePackage=false)
class SecureUserRole implements Serializable {
    
    private static final long serialVersionUID = 1
    
    SecureUser secureUser
    SecureRole secureRole
    
    // Assignment metadata
    Date dateCreated
    Date lastUpdated
    String assignedBy
    String assignmentReason
    Date validFrom
    Date validTo
    boolean active = true
    
    static constraints = {
        secureUser nullable: false
        secureRole nullable: false
        assignedBy nullable: true, maxSize: 50
        assignmentReason nullable: true, maxSize: 255
        validFrom nullable: true
        validTo nullable: true,
                validator: { val, obj ->
                    if (val && obj.validFrom && val.before(obj.validFrom)) {
                        return 'valid.to.before.valid.from'
                    }
                }
    }
    
    static mapping = {
        table 'secure_user_role'
        id composite: ['secureUser', 'secureRole']
        
        // Performance indexes
        secureUser index: 'idx_user_role_user'
        secureRole index: 'idx_user_role_role'
        active index: 'idx_user_role_active'
        validFrom index: 'idx_user_role_valid_from'
        validTo index: 'idx_user_role_valid_to'
        
        // Composite indexes
        indexes = [
            [name: 'idx_user_role_validity', columnNames: ['valid_from', 'valid_to', 'active']],
            [name: 'idx_user_role_assignment', columnNames: ['assigned_by', 'date_created']]
        ]
        
        version false
    }
    
    /**
     * Check if role assignment is currently valid
     */
    boolean isCurrentlyValid() {
        if (!active) {
            return false
        }
        
        Date now = new Date()
        
        // Check valid from date
        if (validFrom && now.before(validFrom)) {
            return false
        }
        
        // Check valid to date
        if (validTo && now.after(validTo)) {
            return false
        }
        
        return true
    }
    
    /**
     * Check if role assignment will be valid on a specific date
     */
    boolean isValidOn(Date date) {
        if (!active) {
            return false
        }
        
        // Check valid from date
        if (validFrom && date.before(validFrom)) {
            return false
        }
        
        // Check valid to date
        if (validTo && date.after(validTo)) {
            return false
        }
        
        return true
    }
    
    /**
     * Activate role assignment
     */
    void activate(String activatedBy, String reason = null) {
        this.active = true
        this.assignedBy = activatedBy
        this.assignmentReason = reason
        this.lastUpdated = new Date()
    }
    
    /**
     * Deactivate role assignment
     */
    void deactivate(String deactivatedBy, String reason = null) {
        this.active = false
        this.assignedBy = deactivatedBy
        this.assignmentReason = reason
        this.lastUpdated = new Date()
    }
    
    /**
     * Set validity period
     */
    void setValidityPeriod(Date from, Date to, String updatedBy) {
        this.validFrom = from
        this.validTo = to
        this.assignedBy = updatedBy
        this.lastUpdated = new Date()
    }
    
    /**
     * Extend validity period
     */
    void extendValidity(Date newValidTo, String updatedBy, String reason = null) {
        this.validTo = newValidTo
        this.assignedBy = updatedBy
        this.assignmentReason = reason
        this.lastUpdated = new Date()
    }
    
    /**
     * Get days until expiry
     */
    Integer getDaysUntilExpiry() {
        if (!validTo) {
            return null // No expiry
        }
        
        Date now = new Date()
        if (now.after(validTo)) {
            return 0 // Already expired
        }
        
        long diffInMillis = validTo.time - now.time
        return (int) (diffInMillis / (24 * 60 * 60 * 1000))
    }
    
    /**
     * Check if assignment is expiring soon (within specified days)
     */
    boolean isExpiringSoon(int days = 30) {
        Integer daysUntilExpiry = getDaysUntilExpiry()
        return daysUntilExpiry != null && daysUntilExpiry <= days && daysUntilExpiry > 0
    }
    
    /**
     * Get assignment duration in days
     */
    Integer getAssignmentDurationDays() {
        if (!validFrom || !validTo) {
            return null
        }
        
        long diffInMillis = validTo.time - validFrom.time
        return (int) (diffInMillis / (24 * 60 * 60 * 1000))
    }
    
    /**
     * Get assignment status
     */
    String getAssignmentStatus() {
        if (!active) {
            return 'INACTIVE'
        }
        
        Date now = new Date()
        
        if (validFrom && now.before(validFrom)) {
            return 'PENDING'
        }
        
        if (validTo && now.after(validTo)) {
            return 'EXPIRED'
        }
        
        if (isExpiringSoon()) {
            return 'EXPIRING_SOON'
        }
        
        return 'ACTIVE'
    }
    
    /**
     * Get status color for UI
     */
    String getStatusColor() {
        switch (getAssignmentStatus()) {
            case 'ACTIVE':
                return '#28a745' // Green
            case 'PENDING':
                return '#ffc107' // Yellow
            case 'EXPIRED':
                return '#dc3545' // Red
            case 'EXPIRING_SOON':
                return '#fd7e14' // Orange
            case 'INACTIVE':
                return '#6c757d' // Gray
            default:
                return '#6c757d' // Default gray
        }
    }
    
    @Override
    boolean equals(other) {
        if (!(other instanceof SecureUserRole)) {
            return false
        }
        
        other.secureUser?.id == secureUser?.id && other.secureRole?.id == secureRole?.id
    }
    
    @Override
    int hashCode() {
        def builder = new HashCodeBuilder()
        if (secureUser) builder.append(secureUser.id)
        if (secureRole) builder.append(secureRole.id)
        builder.toHashCode()
    }
    
    /**
     * Create user-role assignment
     */
    static SecureUserRole create(SecureUser user, SecureRole role, String assignedBy, 
                                String reason = null, Date validFrom = null, Date validTo = null) {
        def userRole = new SecureUserRole(
            secureUser: user,
            secureRole: role,
            assignedBy: assignedBy,
            assignmentReason: reason,
            validFrom: validFrom,
            validTo: validTo,
            active: true
        )
        
        userRole.save(failOnError: true)
        return userRole
    }
    
    /**
     * Remove user-role assignment
     */
    static boolean remove(SecureUser user, SecureRole role) {
        def userRole = SecureUserRole.findBySecureUserAndSecureRole(user, role)
        if (userRole) {
            userRole.delete()
            return true
        }
        return false
    }
    
    /**
     * Get all active assignments for user
     */
    static List<SecureUserRole> findAllActiveByUser(SecureUser user) {
        return SecureUserRole.findAllBySecureUserAndActive(user, true)
    }
    
    /**
     * Get all active assignments for role
     */
    static List<SecureUserRole> findAllActiveByRole(SecureRole role) {
        return SecureUserRole.findAllBySecureRoleAndActive(role, true)
    }
    
    /**
     * Get all currently valid assignments for user
     */
    static List<SecureUserRole> findAllCurrentlyValidByUser(SecureUser user) {
        Date now = new Date()
        return SecureUserRole.createCriteria().list {
            eq('secureUser', user)
            eq('active', true)
            or {
                isNull('validFrom')
                le('validFrom', now)
            }
            or {
                isNull('validTo')
                gt('validTo', now)
            }
        }
    }
    
    /**
     * Get all expiring assignments (within specified days)
     */
    static List<SecureUserRole> findAllExpiring(int days = 30) {
        Date cutoffDate = new Date() + days
        return SecureUserRole.createCriteria().list {
            eq('active', true)
            isNotNull('validTo')
            between('validTo', new Date(), cutoffDate)
        }
    }
    
    /**
     * Get all expired assignments
     */
    static List<SecureUserRole> findAllExpired() {
        Date now = new Date()
        return SecureUserRole.createCriteria().list {
            eq('active', true)
            isNotNull('validTo')
            lt('validTo', now)
        }
    }
    
    /**
     * Cleanup expired assignments
     */
    static int cleanupExpiredAssignments() {
        List<SecureUserRole> expired = findAllExpired()
        expired.each { assignment ->
            assignment.deactivate('SYSTEM', 'Automatic cleanup - assignment expired')
            assignment.save()
        }
        return expired.size()
    }
    
    /**
     * Get assignment statistics
     */
    static Map getAssignmentStatistics() {
        return [
            total: SecureUserRole.count(),
            active: SecureUserRole.countByActive(true),
            inactive: SecureUserRole.countByActive(false),
            expiring: findAllExpiring().size(),
            expired: findAllExpired().size()
        ]
    }
}
