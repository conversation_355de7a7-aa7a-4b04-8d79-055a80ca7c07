package org.icbs.security

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString

/**
 * Password History Domain Class
 * Tracks password history to prevent reuse and ensure compliance
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
@Entity
@Table(name = 'password_history')
@EqualsAndHashCode(includes=['user', 'passwordHash'])
@ToString(includes=['user', 'dateCreated'], includeNames=true, includePackage=false)
class PasswordHistory implements Serializable {
    
    private static final long serialVersionUID = 1
    
    // Password information
    String passwordHash
    String hashAlgorithm = 'BCRYPT'
    Integer passwordVersion = 1
    
    // Audit information
    Date dateCreated = new Date()
    String createdBy
    String changeReason
    
    // Security metadata
    String ipAddress
    String userAgent
    boolean forcedChange = false
    
    // Compliance information
    Integer passwordStrengthScore
    String complianceFlags
    
    // Relationships
    SecureUser user
    
    static constraints = {
        // Password constraints
        passwordHash nullable: false, blank: false, maxSize: 255
        hashAlgorithm nullable: false, maxSize: 20,
                     inList: ['BCRYPT', 'SCRYPT', 'ARGON2', 'PBKDF2']
        passwordVersion min: 1
        
        // Audit constraints
        dateCreated nullable: false
        createdBy nullable: true, maxSize: 50
        changeReason nullable: true, maxSize: 100,
                    inList: [
                        'USER_INITIATED',
                        'POLICY_EXPIRY',
                        'ADMIN_RESET',
                        'SECURITY_BREACH',
                        'FIRST_LOGIN',
                        'COMPLIANCE_REQUIREMENT',
                        'SYSTEM_MIGRATION'
                    ]
        
        // Security metadata constraints
        ipAddress nullable: true, maxSize: 45, // IPv6 compatible
                 validator: { val, obj ->
                     if (val && !val.matches(/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$|^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/)) {
                         return 'invalid.ip.address'
                     }
                 }
        userAgent nullable: true, maxSize: 500
        
        // Compliance constraints
        passwordStrengthScore nullable: true, min: 0, max: 100
        complianceFlags nullable: true, maxSize: 200
        
        // Relationship constraints
        user nullable: false
    }
    
    static mapping = {
        table 'password_history'
        id generator: 'identity'
        
        // Performance indexes
        user index: 'idx_password_history_user'
        passwordHash index: 'idx_password_history_hash'
        dateCreated index: 'idx_password_history_date'
        hashAlgorithm index: 'idx_password_history_algorithm'
        
        // Composite indexes
        indexes = [
            [name: 'idx_password_user_date', columnNames: ['user_id', 'date_created']],
            [name: 'idx_password_user_hash', columnNames: ['user_id', 'password_hash']],
            [name: 'idx_password_compliance', columnNames: ['compliance_flags', 'date_created']]
        ]
        
        version false
    }
    
    /**
     * Check if password is recent (within specified days)
     */
    boolean isRecent(int days = 90) {
        Date cutoffDate = new Date() - days
        return dateCreated.after(cutoffDate)
    }
    
    /**
     * Get password age in days
     */
    int getPasswordAgeDays() {
        long ageInMillis = new Date().time - dateCreated.time
        return (int) (ageInMillis / (24 * 60 * 60 * 1000))
    }
    
    /**
     * Get formatted password age
     */
    String getFormattedAge() {
        int days = getPasswordAgeDays()
        
        if (days < 1) {
            return "Today"
        } else if (days == 1) {
            return "1 day ago"
        } else if (days < 30) {
            return "${days} days ago"
        } else if (days < 365) {
            int months = days / 30
            return "${months} month${months > 1 ? 's' : ''} ago"
        } else {
            int years = days / 365
            return "${years} year${years > 1 ? 's' : ''} ago"
        }
    }
    
    /**
     * Get strength level based on score
     */
    String getStrengthLevel() {
        if (!passwordStrengthScore) {
            return 'UNKNOWN'
        }
        
        if (passwordStrengthScore >= 80) {
            return 'STRONG'
        } else if (passwordStrengthScore >= 60) {
            return 'MEDIUM'
        } else if (passwordStrengthScore >= 40) {
            return 'WEAK'
        } else {
            return 'VERY_WEAK'
        }
    }
    
    /**
     * Get strength color for UI
     */
    String getStrengthColor() {
        switch (getStrengthLevel()) {
            case 'STRONG':
                return '#28a745' // Green
            case 'MEDIUM':
                return '#ffc107' // Yellow
            case 'WEAK':
                return '#fd7e14' // Orange
            case 'VERY_WEAK':
                return '#dc3545' // Red
            default:
                return '#6c757d' // Gray
        }
    }
    
    /**
     * Add compliance flag
     */
    void addComplianceFlag(String flag) {
        if (!complianceFlags) {
            complianceFlags = flag
        } else {
            complianceFlags += ",${flag}"
        }
    }
    
    /**
     * Check if has compliance flag
     */
    boolean hasComplianceFlag(String flag) {
        return complianceFlags?.contains(flag) ?: false
    }
    
    /**
     * Check if password change was forced
     */
    boolean wasForcedChange() {
        return forcedChange || changeReason in ['POLICY_EXPIRY', 'ADMIN_RESET', 'SECURITY_BREACH', 'COMPLIANCE_REQUIREMENT']
    }
    
    /**
     * Create password history entry
     */
    static PasswordHistory createEntry(SecureUser user, String passwordHash, Map options = [:]) {
        def history = new PasswordHistory(
            user: user,
            passwordHash: passwordHash,
            hashAlgorithm: options.hashAlgorithm ?: 'BCRYPT',
            passwordVersion: options.passwordVersion ?: 1,
            createdBy: options.createdBy,
            changeReason: options.changeReason ?: 'USER_INITIATED',
            ipAddress: options.ipAddress,
            userAgent: options.userAgent,
            forcedChange: options.forcedChange ?: false,
            passwordStrengthScore: options.passwordStrengthScore,
            complianceFlags: options.complianceFlags
        )
        
        history.save(failOnError: true)
        return history
    }
    
    /**
     * Check if password was used before by user
     */
    static boolean isPasswordReused(SecureUser user, String passwordHash) {
        return PasswordHistory.countByUserAndPasswordHash(user, passwordHash) > 0
    }
    
    /**
     * Get password history for user
     */
    static List<PasswordHistory> getHistoryForUser(SecureUser user, int max = 10) {
        return PasswordHistory.findAllByUser(user, [max: max, sort: 'dateCreated', order: 'desc'])
    }
    
    /**
     * Get recent password changes (within specified days)
     */
    static List<PasswordHistory> getRecentChanges(int days = 30, int max = 100) {
        Date since = new Date() - days
        return PasswordHistory.findAllByDateCreatedGreaterThan(since, [max: max, sort: 'dateCreated', order: 'desc'])
    }
    
    /**
     * Get password reuse violations
     */
    static List<Map> getPasswordReuseViolations(int historyLimit = 12) {
        def violations = []
        
        // Find users with password reuse
        def users = SecureUser.list()
        users.each { user ->
            def history = getHistoryForUser(user, historyLimit)
            def currentPasswordHash = user.password
            
            // Check if current password matches any in history
            def reuseEntry = history.find { it.passwordHash == currentPasswordHash }
            if (reuseEntry) {
                violations << [
                    user: user,
                    reuseDate: reuseEntry.dateCreated,
                    daysSinceReuse: reuseEntry.getPasswordAgeDays()
                ]
            }
        }
        
        return violations
    }
    
    /**
     * Cleanup old password history entries
     */
    static int cleanupOldEntries(int retentionDays = 365) {
        Date cutoffDate = new Date() - retentionDays
        def oldEntries = PasswordHistory.findAllByDateCreatedLessThan(cutoffDate)
        
        oldEntries.each { entry ->
            entry.delete()
        }
        
        return oldEntries.size()
    }
    
    /**
     * Get password history statistics
     */
    static Map getPasswordHistoryStatistics() {
        def totalEntries = PasswordHistory.count()
        def recentChanges = getRecentChanges(30).size()
        def forcedChanges = PasswordHistory.countByForcedChange(true)
        
        // Calculate average password age
        def avgAge = PasswordHistory.createCriteria().get {
            projections {
                avg('passwordAgeDays')
            }
        } ?: 0
        
        return [
            totalEntries: totalEntries,
            recentChanges: recentChanges,
            forcedChanges: forcedChanges,
            averagePasswordAge: Math.round(avgAge as Double),
            reuseViolations: getPasswordReuseViolations().size()
        ]
    }
    
    /**
     * Get compliance report
     */
    static Map getComplianceReport() {
        def report = [:]
        
        // Password age compliance (90 days)
        def oldPasswords = PasswordHistory.createCriteria().list {
            lt('dateCreated', new Date() - 90)
            eq('user.enabled', true)
        }
        
        report.passwordAgeViolations = oldPasswords.size()
        
        // Password reuse compliance
        report.reuseViolations = getPasswordReuseViolations().size()
        
        // Weak password compliance
        def weakPasswords = PasswordHistory.createCriteria().list {
            lt('passwordStrengthScore', 60)
            gt('dateCreated', new Date() - 30) // Recent weak passwords
        }
        
        report.weakPasswordViolations = weakPasswords.size()
        
        return report
    }
}
