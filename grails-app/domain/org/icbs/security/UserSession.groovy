package org.icbs.security

import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import org.icbs.admin.UserMaster

/**
 * User Session Management Domain Class
 * Tracks active user sessions for security and audit purposes
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
@Entity
@Table(name = 'user_session')
@EqualsAndHashCode(includes=['sessionId'])
@ToString(includes=['sessionId', 'username', 'isActive'], includeNames=true, includePackage=false)
class UserSession implements Serializable {

    private static final long serialVersionUID = 1

    // Legacy compatibility
    UserMaster userMaster
    Date login
    Date logout

    // Session identification
    String sessionId
    String username
    String userId

    // Session timing
    Date sessionStart = new Date()
    Date sessionEnd
    Date lastActivity = new Date()
    Integer timeoutMinutes = 30

    // Session status
    boolean isActive = true
    String sessionStatus = 'ACTIVE'
    String terminationReason

    // Network information
    String ipAddress
    String remoteHost
    String userAgent
    String browserInfo
    String deviceInfo

    // Security information
    String authenticationMethod = 'PASSWORD'
    boolean mfaVerified = false
    String securityLevel = 'STANDARD'
    Set<String> grantedPermissions = []

    // Session data
    Map<String, Object> sessionData = [:]
    String lastAccessedUrl
    Integer requestCount = 0

    // Relationships
    SecureUser user

    static constraints = {
        // Legacy constraints
        logout nullable: true
        userMaster nullable: true

        // Session identification constraints
        sessionId nullable: true, unique: true, maxSize: 128
        username nullable: true, maxSize: 50
        userId nullable: true, maxSize: 50

        // Timing constraints
        sessionStart nullable: true
        sessionEnd nullable: true
        lastActivity nullable: true
        timeoutMinutes nullable: true, min: 5, max: 480

        // Status constraints
        sessionStatus nullable: true,
                     inList: [
                         'ACTIVE',
                         'EXPIRED',
                         'TERMINATED',
                         'LOCKED',
                         'SUSPENDED'
                     ]
        terminationReason nullable: true, maxSize: 100

        // Network constraints
        ipAddress nullable: true, maxSize: 45
        remoteHost nullable: true, maxSize: 255
        userAgent nullable: true, maxSize: 500
        browserInfo nullable: true, maxSize: 200
        deviceInfo nullable: true, maxSize: 200

        // Security constraints
        authenticationMethod nullable: true,
                           inList: [
                               'PASSWORD',
                               'MFA',
                               'SSO',
                               'CERTIFICATE',
                               'BIOMETRIC',
                               'TOKEN'
                           ]
        securityLevel nullable: true,
                     inList: ['STANDARD', 'ELEVATED', 'PRIVILEGED']
        grantedPermissions nullable: true

        // Session data constraints
        sessionData nullable: true
        lastAccessedUrl nullable: true, maxSize: 500
        requestCount nullable: true, min: 0

        // Relationship constraints
        user nullable: true
    }

    static mapping = {
        // Legacy mapping
        id sqlType: 'int', generator: 'increment'
        userMaster sqlType: 'int'

        table 'user_session'

        // Large data fields
        sessionData type: 'jsonb'
        grantedPermissions type: 'jsonb'

        // Performance indexes
        sessionId index: 'idx_session_id'
        username index: 'idx_session_username'
        isActive index: 'idx_session_active'
        sessionStatus index: 'idx_session_status'
        lastActivity index: 'idx_session_last_activity'
        ipAddress index: 'idx_session_ip'

        version false
    }

    /**
     * Check if session is expired
     */
    boolean isExpired() {
        if (!isActive || sessionStatus != 'ACTIVE') {
            return true
        }

        if (!lastActivity || !timeoutMinutes) {
            return false
        }

        Date expiryTime = new Date(lastActivity.time + (timeoutMinutes * 60 * 1000))
        return new Date().after(expiryTime)
    }

    /**
     * Update last activity
     */
    void updateActivity(String url = null) {
        this.lastActivity = new Date()
        this.requestCount = (this.requestCount ?: 0) + 1

        if (url) {
            this.lastAccessedUrl = url
        }
    }

    /**
     * Terminate session
     */
    void terminate(String reason) {
        this.isActive = false
        this.sessionStatus = 'TERMINATED'
        this.sessionEnd = new Date()
        this.logout = new Date() // Legacy compatibility
        this.terminationReason = reason
    }

    /**
     * Get remaining session time in minutes
     */
    Integer getRemainingMinutes() {
        if (!isActive || sessionStatus != 'ACTIVE' || !lastActivity || !timeoutMinutes) {
            return 0
        }

        Date expiryTime = new Date(lastActivity.time + (timeoutMinutes * 60 * 1000))
        long remainingMillis = expiryTime.time - new Date().time

        if (remainingMillis <= 0) {
            return 0
        }

        return (int) (remainingMillis / (60 * 1000))
    }

    /**
     * Create new session with legacy compatibility
     */
    static UserSession createSession(UserMaster userMaster, String sessionId = null, String ipAddress = null) {
        def session = new UserSession(
            userMaster: userMaster,
            login: new Date(),
            sessionId: sessionId ?: UUID.randomUUID().toString(),
            username: userMaster?.username,
            userId: userMaster?.id?.toString(),
            ipAddress: ipAddress,
            sessionStart: new Date(),
            lastActivity: new Date(),
            timeoutMinutes: 30,
            isActive: true,
            sessionStatus: 'ACTIVE'
        )

        session.save(failOnError: true)
        return session
    }

    /**
     * Get active sessions for user (legacy compatibility)
     */
    static List<UserSession> getActiveSessionsForUser(UserMaster userMaster) {
        return UserSession.findAllByUserMasterAndIsActive(userMaster, true)
    }

    /**
     * Cleanup expired sessions
     */
    static int cleanupExpiredSessions() {
        Date cutoffTime = new Date() - 1 // 1 day ago
        List<UserSession> expiredSessions = UserSession.createCriteria().list {
            eq('isActive', true)
            or {
                eq('sessionStatus', 'ACTIVE')
                isNull('sessionStatus')
            }
            or {
                and {
                    isNotNull('lastActivity')
                    lt('lastActivity', cutoffTime)
                }
                and {
                    isNull('lastActivity')
                    isNotNull('login')
                    lt('login', cutoffTime)
                }
            }
        }

        expiredSessions.each { session ->
            session.terminate('TIMEOUT')
            session.save()
        }

        return expiredSessions.size()
    }
}
