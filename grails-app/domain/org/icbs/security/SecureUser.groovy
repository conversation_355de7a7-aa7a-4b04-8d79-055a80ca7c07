package org.icbs.security

import grails.plugin.springsecurity.SpringSecurityUtils
import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import org.icbs.admin.Branch
import org.icbs.admin.Department

/**
 * Modern Spring Security User Domain Class
 * Implements comprehensive security features for banking systems
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since Grails 7.0
 */
@Entity
@Table(name = 'secure_user')
@EqualsAndHashCode(includes='username')
@ToString(includes='username', includeNames=true, includePackage=false)
class SecureUser implements Serializable {
    
    private static final long serialVersionUID = 1
    
    // Basic Authentication Fields
    String username
    String password
    String email
    String firstName
    String lastName
    
    // Account Status Fields
    boolean enabled = true
    boolean accountExpired = false
    boolean accountLocked = false
    boolean passwordExpired = false
    
    // Audit Fields
    Date dateCreated
    Date lastUpdated
    Date lastLogin
    String lastLoginIp
    Integer failedLoginAttempts = 0
    Date lastFailedLogin
    Date passwordLastChanged
    
    // Banking Specific Fields
    String employeeId
    Branch branch
    Department department
    Set<String> permissions = []
    
    // Security Enhancement Fields
    String mfaSecret
    boolean mfaEnabled = false
    String recoveryEmail
    String phoneNumber
    Date lastPasswordChange
    Integer passwordVersion = 1
    
    // Session Management
    String currentSessionId
    Date sessionStartTime
    Integer maxConcurrentSessions = 1
    
    static hasMany = [
        authorities: SecureRole,
        sessions: UserSession,
        auditLogs: SecurityAuditLog,
        passwordHistory: PasswordHistory
    ]
    
    static belongsTo = [branch: Branch, department: Department]
    
    static constraints = {
        // Username constraints
        username nullable: false, blank: false, unique: true, maxSize: 50, 
                matches: /^[a-zA-Z0-9._-]+$/, 
                validator: { val, obj ->
                    if (val && val.length() < 3) {
                        return 'username.too.short'
                    }
                    if (val && val.toLowerCase() in ['admin', 'root', 'system', 'test']) {
                        return 'username.reserved'
                    }
                }
        
        // Password constraints (will be hashed)
        password nullable: false, blank: false, minSize: 8, maxSize: 255,
                validator: { val, obj ->
                    if (!val) return true // Let nullable/blank handle null/empty
                    
                    // Password complexity requirements for banking
                    if (!val.matches(/.*[A-Z].*/)) {
                        return 'password.no.uppercase'
                    }
                    if (!val.matches(/.*[a-z].*/)) {
                        return 'password.no.lowercase'
                    }
                    if (!val.matches(/.*\d.*/)) {
                        return 'password.no.digit'
                    }
                    if (!val.matches(/.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?].*/)) {
                        return 'password.no.special'
                    }
                    if (val.contains(obj.username)) {
                        return 'password.contains.username'
                    }
                }
        
        // Email constraints
        email nullable: false, blank: false, email: true, unique: true, maxSize: 100
        
        // Name constraints
        firstName nullable: false, blank: false, maxSize: 50,
                 matches: /^[a-zA-Z\s'-]+$/
        lastName nullable: false, blank: false, maxSize: 50,
                matches: /^[a-zA-Z\s'-]+$/
        
        // Banking specific constraints
        employeeId nullable: true, unique: true, maxSize: 20,
                  matches: /^[A-Z0-9-]+$/
        branch nullable: false
        department nullable: false
        
        // Security constraints
        lastLogin nullable: true
        lastLoginIp nullable: true, maxSize: 45, // IPv6 compatible
                   validator: { val, obj ->
                       if (val && !val.matches(/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$|^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/)) {
                           return 'invalid.ip.address'
                       }
                   }
        failedLoginAttempts min: 0, max: 10
        lastFailedLogin nullable: true
        passwordLastChanged nullable: true
        
        // MFA constraints
        mfaSecret nullable: true, maxSize: 32
        recoveryEmail nullable: true, email: true, maxSize: 100
        phoneNumber nullable: true, maxSize: 20,
                   matches: /^\+?[1-9]\d{1,14}$/ // E.164 format
        
        // Session constraints
        currentSessionId nullable: true, maxSize: 128
        sessionStartTime nullable: true
        maxConcurrentSessions min: 1, max: 5
        
        // Version constraints
        passwordVersion min: 1
    }
    
    static mapping = {
        table 'secure_user'
        id generator: 'identity'
        password column: '`password`'
        
        // Relationship mappings
        authorities joinTable: [
            name: 'secure_user_role', 
            key: 'user_id', 
            column: 'role_id'
        ]
        sessions cascade: 'all-delete-orphan'
        auditLogs cascade: 'all-delete-orphan'
        passwordHistory cascade: 'all-delete-orphan'
        
        // Performance indexes
        username index: 'idx_secure_user_username'
        email index: 'idx_secure_user_email'
        employeeId index: 'idx_secure_user_employee_id'
        lastLogin index: 'idx_secure_user_last_login'
        enabled index: 'idx_secure_user_enabled'
        
        // Composite indexes
        indexes = [
            [name: 'idx_user_branch_dept', columnNames: ['branch_id', 'department_id']],
            [name: 'idx_user_status', columnNames: ['enabled', 'account_locked', 'account_expired']],
            [name: 'idx_user_security', columnNames: ['failed_login_attempts', 'last_failed_login']]
        ]
        
        version false
    }
    
    /**
     * Get authorities for Spring Security
     */
    Set<SecureRole> getAuthorities() {
        (SecureUserRole.findAllBySecureUser(this) as List<SecureUserRole>)*.secureRole as Set<SecureRole>
    }
    
    /**
     * Check if user has specific role
     */
    boolean hasRole(String roleName) {
        return authorities.any { it.authority == roleName }
    }
    
    /**
     * Check if user has any of the specified roles
     */
    boolean hasAnyRole(String... roleNames) {
        return authorities.any { it.authority in roleNames }
    }
    
    /**
     * Get user's full name
     */
    String getFullName() {
        return "${firstName} ${lastName}".trim()
    }
    
    /**
     * Check if account is locked due to failed login attempts
     */
    boolean isAccountLockedByFailedAttempts() {
        return failedLoginAttempts >= 5
    }
    
    /**
     * Check if password needs to be changed (90 days policy)
     */
    boolean isPasswordExpiredByPolicy() {
        if (!passwordLastChanged) return true
        
        Date expiryDate = new Date(passwordLastChanged.time + (90 * 24 * 60 * 60 * 1000L))
        return new Date().after(expiryDate)
    }
    
    /**
     * Record successful login
     */
    void recordSuccessfulLogin(String ipAddress, String sessionId) {
        this.lastLogin = new Date()
        this.lastLoginIp = ipAddress
        this.currentSessionId = sessionId
        this.sessionStartTime = new Date()
        this.failedLoginAttempts = 0
        this.lastFailedLogin = null
    }
    
    /**
     * Record failed login attempt
     */
    void recordFailedLogin(String ipAddress) {
        this.failedLoginAttempts = (this.failedLoginAttempts ?: 0) + 1
        this.lastFailedLogin = new Date()
        this.lastLoginIp = ipAddress
        
        // Lock account after 5 failed attempts
        if (this.failedLoginAttempts >= 5) {
            this.accountLocked = true
        }
    }
    
    /**
     * Reset failed login attempts
     */
    void resetFailedLoginAttempts() {
        this.failedLoginAttempts = 0
        this.lastFailedLogin = null
        this.accountLocked = false
    }
    
    /**
     * Update password with history tracking
     */
    void updatePassword(String newPassword) {
        // Save current password to history
        if (this.password) {
            new PasswordHistory(
                user: this,
                passwordHash: this.password,
                dateCreated: new Date()
            ).save()
        }
        
        this.password = newPassword
        this.passwordLastChanged = new Date()
        this.passwordVersion = (this.passwordVersion ?: 0) + 1
        this.passwordExpired = false
    }
    
    /**
     * Check if password was used before (prevent reuse)
     */
    boolean isPasswordReused(String newPasswordHash) {
        return PasswordHistory.countByUserAndPasswordHash(this, newPasswordHash) > 0
    }
    
    /**
     * Enable MFA for user
     */
    void enableMFA(String secret) {
        this.mfaSecret = secret
        this.mfaEnabled = true
    }
    
    /**
     * Disable MFA for user
     */
    void disableMFA() {
        this.mfaSecret = null
        this.mfaEnabled = false
    }
    
    /**
     * Get display name for UI
     */
    String getDisplayName() {
        return fullName ?: username
    }
    
    /**
     * Check if user is from specific branch
     */
    boolean isFromBranch(String branchCode) {
        return branch?.code == branchCode
    }
    
    /**
     * Get user permissions as list
     */
    List<String> getPermissionsList() {
        return permissions?.toList() ?: []
    }
    
    /**
     * Add permission to user
     */
    void addPermission(String permission) {
        if (!permissions) {
            permissions = [] as Set
        }
        permissions.add(permission)
    }
    
    /**
     * Remove permission from user
     */
    void removePermission(String permission) {
        permissions?.remove(permission)
    }
    
    /**
     * Check if user has specific permission
     */
    boolean hasPermission(String permission) {
        return permissions?.contains(permission) ?: false
    }
}
